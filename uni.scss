/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */

/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */

/* 现代化颜色变量 - 与设计系统保持一致 */

/* 主色调 */
$uni-color-primary: #1890FF;
$uni-color-primary-light: #40A9FF;
$uni-color-primary-dark: #096DD9;

/* 行为相关颜色 */
$uni-color-success: #52C41A;
$uni-color-warning: #FA8C16;
$uni-color-error: #FF4D4F;
$uni-color-info: #1890FF;

/* 文字基本颜色 */
$uni-text-color: #262626; // 主文字色
$uni-text-color-inverse: #FFFFFF; // 反色
$uni-text-color-secondary: #595959; // 次要文字色
$uni-text-color-tertiary: #8C8C8C; // 辅助文字色
$uni-text-color-grey: #BFBFBF; // 辅助灰色
$uni-text-color-placeholder: #BFBFBF; // 占位符颜色
$uni-text-color-disable: #D9D9D9; // 禁用文字色

/* 背景颜色 */
$uni-bg-color: #FFFFFF; // 主背景色
$uni-bg-color-secondary: #FAFAFA; // 次要背景色
$uni-bg-color-tertiary: #F5F5F5; // 第三背景色
$uni-bg-color-grey: #F5F5F5; // 灰色背景
$uni-bg-color-hover: #F0F0F0; // 点击状态颜色
$uni-bg-color-mask: rgba(0, 0, 0, 0.45); // 遮罩颜色

/* 边框颜色 */
$uni-border-color: #D9D9D9; // 主边框色
$uni-border-color-light: #F0F0F0; // 浅边框色
$uni-border-color-dark: #BFBFBF; // 深边框色

/* 现代化尺寸变量 */

/* 文字尺寸 (rpx单位) */
$uni-font-size-xs: 20rpx;
$uni-font-size-sm: 24rpx;
$uni-font-size-base: 28rpx;
$uni-font-size-lg: 32rpx;
$uni-font-size-xl: 36rpx;
$uni-font-size-2xl: 40rpx;
$uni-font-size-3xl: 48rpx;

/* 图片尺寸 (rpx单位) */
$uni-img-size-xs: 32rpx;
$uni-img-size-sm: 48rpx;
$uni-img-size-base: 64rpx;
$uni-img-size-lg: 96rpx;
$uni-img-size-xl: 128rpx;

/* 现代化圆角 (rpx单位) */
$uni-border-radius-xs: 4rpx;
$uni-border-radius-sm: 8rpx;
$uni-border-radius-base: 12rpx;
$uni-border-radius-lg: 16rpx;
$uni-border-radius-xl: 24rpx;
$uni-border-radius-2xl: 32rpx;
$uni-border-radius-circle: 50%;
$uni-border-radius-full: 9999rpx;

/* 现代化间距系统 (rpx单位) */
$uni-spacing-xs: 8rpx;
$uni-spacing-sm: 16rpx;
$uni-spacing-base: 24rpx;
$uni-spacing-lg: 32rpx;
$uni-spacing-xl: 48rpx;
$uni-spacing-2xl: 64rpx;
$uni-spacing-3xl: 96rpx;

/* 页面间距 */
$uni-page-padding: 24rpx;
$uni-section-padding: 32rpx;
$uni-card-padding: 32rpx;
$uni-item-padding: 24rpx;

/* 透明度 */
$uni-opacity-disabled: 0.4; // 组件禁用态的透明度
$uni-opacity-hover: 0.8; // 悬停态透明度

/* 阴影 */
$uni-shadow-xs: 0 2rpx 8rpx rgba(0, 0, 0, 0.06);
$uni-shadow-sm: 0 2rpx 12rpx rgba(0, 0, 0, 0.08);
$uni-shadow-base: 0 4rpx 16rpx rgba(0, 0, 0, 0.10);
$uni-shadow-lg: 0 8rpx 24rpx rgba(0, 0, 0, 0.12);
$uni-shadow-xl: 0 12rpx 32rpx rgba(0, 0, 0, 0.15);

/* 动画时长 */
$uni-duration-fast: 0.15s;
$uni-duration-base: 0.3s;
$uni-duration-slow: 0.5s;

/* 现代化文章场景相关 */
$uni-color-title: #262626; // 文章标题颜色
$uni-font-size-title: 40rpx; // 标题字体大小
$uni-color-subtitle: #595959; // 二级标题颜色
$uni-font-size-subtitle: 32rpx; // 副标题字体大小
$uni-color-paragraph: #8C8C8C; // 文章段落颜色
$uni-font-size-paragraph: 28rpx; // 段落字体大小

/* 状态色扩展 */
$uni-color-success-light: #73D13D;
$uni-color-success-dark: #389E0D;
$uni-color-warning-light: #FFA940;
$uni-color-warning-dark: #D46B08;
$uni-color-error-light: #FF7875;
$uni-color-error-dark: #CF1322;
$uni-color-info-light: #40A9FF;
$uni-color-info-dark: #096DD9;
