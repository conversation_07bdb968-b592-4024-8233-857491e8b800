/*
 * 翼呼应用现代化设计系统
 * Design System for YiHu App Modernization
 * 
 * 包含：色彩体系、字体规范、间距规范、组件规范
 */

/* ==================
   CSS 变量定义 - 色彩体系
 ==================== */
:root {
  /* 主色调 - Primary Colors */
  --primary-color: #1890FF;
  --primary-light: #40A9FF;
  --primary-lighter: #69C0FF;
  --primary-dark: #096DD9;
  --primary-darker: #0050B3;
  
  /* 辅助色 - Secondary Colors */
  --secondary-color: #722ED1;
  --secondary-light: #9254DE;
  --secondary-dark: #531DAB;
  
  /* 中性色 - Neutral Colors */
  --white: #FFFFFF;
  --gray-50: #FAFAFA;
  --gray-100: #F5F5F5;
  --gray-200: #F0F0F0;
  --gray-300: #D9D9D9;
  --gray-400: #BFBFBF;
  --gray-500: #8C8C8C;
  --gray-600: #595959;
  --gray-700: #434343;
  --gray-800: #262626;
  --gray-900: #1F1F1F;
  --black: #000000;
  
  /* 状态色 - Status Colors */
  --success-color: #52C41A;
  --success-light: #73D13D;
  --success-dark: #389E0D;
  --success-bg: #F6FFED;
  
  --warning-color: #FA8C16;
  --warning-light: #FFA940;
  --warning-dark: #D46B08;
  --warning-bg: #FFF7E6;
  
  --error-color: #FF4D4F;
  --error-light: #FF7875;
  --error-dark: #CF1322;
  --error-bg: #FFF2F0;
  
  --info-color: #1890FF;
  --info-light: #40A9FF;
  --info-dark: #096DD9;
  --info-bg: #E6F7FF;
  
  /* 文字色彩 - Text Colors */
  --text-primary: #262626;
  --text-secondary: #595959;
  --text-tertiary: #8C8C8C;
  --text-quaternary: #BFBFBF;
  --text-white: #FFFFFF;
  --text-link: #1890FF;
  --text-link-hover: #40A9FF;
  
  /* 背景色 - Background Colors */
  --bg-primary: #FFFFFF;
  --bg-secondary: #FAFAFA;
  --bg-tertiary: #F5F5F5;
  --bg-quaternary: #F0F0F0;
  --bg-mask: rgba(0, 0, 0, 0.45);
  
  /* 边框色 - Border Colors */
  --border-color: #D9D9D9;
  --border-color-light: #F0F0F0;
  --border-color-dark: #BFBFBF;
}

/* ==================
   字体规范 - Typography
 ==================== */
:root {
  /* 字体大小 - Font Sizes (rpx) */
  --font-size-xs: 20rpx;
  --font-size-sm: 24rpx;
  --font-size-base: 28rpx;
  --font-size-lg: 32rpx;
  --font-size-xl: 36rpx;
  --font-size-2xl: 40rpx;
  --font-size-3xl: 48rpx;
  
  /* 行高 - Line Heights */
  --line-height-tight: 1.2;
  --line-height-normal: 1.4;
  --line-height-relaxed: 1.6;
  --line-height-loose: 1.8;
  
  /* 字重 - Font Weights */
  --font-weight-light: 300;
  --font-weight-normal: 400;
  --font-weight-medium: 500;
  --font-weight-semibold: 600;
  --font-weight-bold: 700;
}

/* ==================
   间距规范 - Spacing
 ==================== */
:root {
  /* 基础间距单位 */
  --space-unit: 8rpx;
  
  /* 间距变量 */
  --space-xs: 8rpx;   /* 1 unit */
  --space-sm: 16rpx;  /* 2 units */
  --space-md: 24rpx;  /* 3 units */
  --space-lg: 32rpx;  /* 4 units */
  --space-xl: 48rpx;  /* 6 units */
  --space-2xl: 64rpx; /* 8 units */
  --space-3xl: 96rpx; /* 12 units */
  
  /* 页面间距 */
  --page-padding: 24rpx;
  --section-padding: 32rpx;
  --card-padding: 32rpx;
  --item-padding: 24rpx;
}

/* ==================
   圆角规范 - Border Radius
 ==================== */
:root {
  --radius-xs: 4rpx;
  --radius-sm: 8rpx;
  --radius-md: 12rpx;
  --radius-lg: 16rpx;
  --radius-xl: 24rpx;
  --radius-2xl: 32rpx;
  --radius-full: 9999rpx;
}

/* ==================
   阴影规范 - Shadows
 ==================== */
:root {
  --shadow-xs: 0 2rpx 8rpx rgba(0, 0, 0, 0.06);
  --shadow-sm: 0 2rpx 12rpx rgba(0, 0, 0, 0.08);
  --shadow-md: 0 4rpx 16rpx rgba(0, 0, 0, 0.10);
  --shadow-lg: 0 8rpx 24rpx rgba(0, 0, 0, 0.12);
  --shadow-xl: 0 12rpx 32rpx rgba(0, 0, 0, 0.15);
  --shadow-2xl: 0 16rpx 48rpx rgba(0, 0, 0, 0.20);
}

/* ==================
   动画规范 - Animations
 ==================== */
:root {
  /* 动画时长 */
  --duration-fast: 0.15s;
  --duration-normal: 0.3s;
  --duration-slow: 0.5s;
  
  /* 动画曲线 */
  --ease-in: cubic-bezier(0.4, 0, 1, 1);
  --ease-out: cubic-bezier(0, 0, 0.2, 1);
  --ease-in-out: cubic-bezier(0.4, 0, 0.2, 1);
  --ease-bounce: cubic-bezier(0.68, -0.55, 0.265, 1.55);
}

/* ==================
   现代化工具类 - Utility Classes
 ==================== */

/* 文字样式 */
.text-primary { color: var(--text-primary) !important; }
.text-secondary { color: var(--text-secondary) !important; }
.text-tertiary { color: var(--text-tertiary) !important; }
.text-white { color: var(--text-white) !important; }
.text-link { color: var(--text-link) !important; }

/* 背景样式 */
.bg-primary { background-color: var(--bg-primary) !important; }
.bg-secondary { background-color: var(--bg-secondary) !important; }
.bg-tertiary { background-color: var(--bg-tertiary) !important; }

/* 现代化按钮样式 */
.btn-modern {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: var(--space-sm) var(--space-lg);
  border-radius: var(--radius-md);
  font-size: var(--font-size-base);
  font-weight: var(--font-weight-medium);
  line-height: var(--line-height-normal);
  transition: all var(--duration-normal) var(--ease-out);
  border: none;
  cursor: pointer;
}

.btn-primary {
  background-color: var(--primary-color);
  color: var(--text-white);
  box-shadow: var(--shadow-sm);
}

.btn-primary:hover {
  background-color: var(--primary-light);
  box-shadow: var(--shadow-md);
  transform: translateY(-2rpx);
}

/* 现代化卡片样式 */
.card-modern {
  background-color: var(--bg-primary);
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-sm);
  padding: var(--card-padding);
  transition: all var(--duration-normal) var(--ease-out);
}

.card-modern:hover {
  box-shadow: var(--shadow-md);
  transform: translateY(-4rpx);
}

/* 现代化输入框样式 */
.input-modern {
  width: 100%;
  padding: var(--space-md);
  border: 2rpx solid var(--border-color);
  border-radius: var(--radius-md);
  font-size: var(--font-size-base);
  line-height: var(--line-height-normal);
  transition: all var(--duration-normal) var(--ease-out);
  background-color: var(--bg-primary);
}

.input-modern:focus {
  border-color: var(--primary-color);
  box-shadow: 0 0 0 6rpx rgba(24, 144, 255, 0.1);
  outline: none;
}

/* 状态指示器 */
.status-indicator {
  display: inline-flex;
  align-items: center;
  padding: var(--space-xs) var(--space-sm);
  border-radius: var(--radius-full);
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
}

.status-success {
  background-color: var(--success-bg);
  color: var(--success-dark);
}

.status-warning {
  background-color: var(--warning-bg);
  color: var(--warning-dark);
}

.status-error {
  background-color: var(--error-bg);
  color: var(--error-dark);
}

.status-info {
  background-color: var(--info-bg);
  color: var(--info-dark);
}
