<!--template部分-->
<template>
	<view class="content">
		<view>{{nums}}</view>
	</view>
</template>
<!--script部分-->
<script>
	export default {
		data() {
			return {
				nums: '00:00:00',
				timer: null
			}
		},
		mounted() {},
		methods: {
			appendZero(obj) {
				if (obj < 10) return "0" + "" + obj;
				else return obj;
			},
			startTimer() {
				let hour, minute, second; /*时 分 秒*/
				hour = minute = second = 0; //初始化
				let millisecond = 0; //毫秒
				this.timer = setInterval(() => {
					millisecond = millisecond + 1000;
					// console.log("---millisecond----"+millisecond);
					if (millisecond >= 1000) {
						millisecond = 0;
						second = second + 1;
					}
					if (second >= 60) {
						second = 0;
						minute = minute + 1;
					}

					if (minute >= 60) {
						minute = 0;
						hour = hour + 1;
					}
					this.nums = this.appendZero(hour) + ':' + this.appendZero(minute) + ':' + this.appendZero(second);
					// console.log("-------"+hour+'时'+minute+'分'+second+'秒');
				}, 1000);
			},
			stopTimer() {
				clearInterval(this.timer);
				this.timer = null;
				this.nums = "00:00:00"
			}
		},
		beforeDestroy() {
			if (this.timer) {
				clearInterval(this.timer);
				this.timer = null;
				this.nums = "00:00:00"
			}
		}
	}
</script>
<!--style部分-->
<style>
</style>
