<script>
	import Vue from 'vue'
	import {
		initWs, reConnect, closeConnect
	} from './utils/useSocket.js'
	export default {
		onLaunch: function() {
			uni.getSystemInfo({
				success: function(e) {
					// #ifndef MP
					Vue.prototype.StatusBar = e.statusBarHeight;
					if (e.platform == 'android') {
						Vue.prototype.CustomBar = e.statusBarHeight + 50;
					} else {
						Vue.prototype.CustomBar = e.statusBarHeight + 45;
					};
					// #endif
					// #ifdef MP-WEIXIN
					Vue.prototype.StatusBar = e.statusBarHeight;
					let custom = wx.getMenuButtonBoundingClientRect();
					Vue.prototype.Custom = custom;
					Vue.prototype.CustomBar = custom.bottom + custom.top - e.statusBarHeight;
					// #endif		
					// #ifdef MP-ALIPAY
					Vue.prototype.StatusBar = e.statusBarHeight;
					Vue.prototype.CustomBar = e.statusBarHeight + e.titleBarHeight;
					// #endif
				}
			})
			
		},
		onShow: function() {
			var that = this
			if (this.isHide) {
				// reConnect('show')
			} else {
				// initWs()
			}
			console.log('App show')
		},
		onHide: function() {
			console.log('App Hide')
		}
	}
</script>

<style>
	/*每个页面公共css */
	@import "colorui/main.css";
	@import "colorui/icon.css";
	@import "styles/icon.css";
	@import "styles/design-system.css";

	/* 全局样式重置和现代化 */
	page {
		background-color: var(--bg-secondary);
		font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', 'Helvetica Neue', Helvetica, Arial, sans-serif;
		font-size: var(--font-size-base);
		line-height: var(--line-height-normal);
		color: var(--text-primary);
	}

	/* 现代化滚动页面样式 */
	.scrollPage {
		background-color: var(--bg-secondary);
		min-height: 100vh;
	}

	/* 现代化页面容器 */
	.page-container {
		padding: var(--page-padding);
		background-color: var(--bg-secondary);
		min-height: 100vh;
	}

	/* 现代化卡片容器 */
	.modern-card {
		background-color: var(--bg-primary);
		border-radius: var(--radius-lg);
		box-shadow: var(--shadow-sm);
		margin-bottom: var(--space-md);
		overflow: hidden;
		transition: all var(--duration-normal) var(--ease-out);
	}

	.modern-card:hover {
		box-shadow: var(--shadow-md);
		transform: translateY(-2rpx);
	}

	/* 现代化列表项 */
	.modern-list-item {
		padding: var(--item-padding);
		border-bottom: 1rpx solid var(--border-color-light);
		transition: background-color var(--duration-fast) var(--ease-out);
	}

	.modern-list-item:last-child {
		border-bottom: none;
	}

	.modern-list-item:active {
		background-color: var(--bg-tertiary);
	}

	/* 现代化按钮样式覆盖 */
	.cu-btn.modern-primary {
		background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-light) 100%);
		color: var(--text-white);
		border-radius: var(--radius-md);
		box-shadow: var(--shadow-sm);
		font-weight: var(--font-weight-medium);
		transition: all var(--duration-normal) var(--ease-out);
	}

	.cu-btn.modern-primary:active {
		background: linear-gradient(135deg, var(--primary-dark) 0%, var(--primary-color) 100%);
		box-shadow: var(--shadow-xs);
		transform: translateY(1rpx);
	}

	/* 现代化标签样式 */
	.modern-tag {
		display: inline-flex;
		align-items: center;
		padding: var(--space-xs) var(--space-sm);
		border-radius: var(--radius-full);
		font-size: var(--font-size-sm);
		font-weight: var(--font-weight-medium);
		line-height: 1;
	}

	.modern-tag.primary {
		background-color: var(--info-bg);
		color: var(--info-dark);
	}

	.modern-tag.success {
		background-color: var(--success-bg);
		color: var(--success-dark);
	}

	.modern-tag.warning {
		background-color: var(--warning-bg);
		color: var(--warning-dark);
	}

	.modern-tag.error {
		background-color: var(--error-bg);
		color: var(--error-dark);
	}

	/* 底部组件现代化 */
	.footer-box {
		margin-top: auto;
		width: 100%;
		background-color: var(--bg-primary);
		border-top: 1rpx solid var(--border-color-light);
	}

	.footer {
		height: 100rpx;
		position: relative;
		line-height: 100rpx;
		width: 100%;
		display: flex;
		align-items: center;
		justify-content: center;
	}

	.footer .line {
		display: block;
		height: 2rpx;
		position: absolute;
		top: 50%;
		width: 90%;
		left: 5%;
		background: linear-gradient(90deg, transparent 0%, var(--border-color) 50%, transparent 100%);
	}

	.footer .text {
		display: inline-block;
		font-size: var(--font-size-sm);
		background: var(--bg-primary);
		padding: 0 var(--space-lg);
		text-align: center;
		position: relative;
		z-index: 2;
		color: var(--text-tertiary);
		font-weight: var(--font-weight-normal);
	}

	/* 现代化导航栏样式 */
	.modern-navbar {
		background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-light) 100%);
		color: var(--text-white);
		box-shadow: var(--shadow-sm);
	}

	/* 现代化搜索框样式 */
	.modern-search {
		background-color: var(--bg-primary);
		border-radius: var(--radius-full);
		box-shadow: var(--shadow-xs);
		transition: all var(--duration-normal) var(--ease-out);
	}

	.modern-search:focus-within {
		box-shadow: var(--shadow-sm);
		transform: translateY(-1rpx);
	}

	/* 现代化状态指示器 */
	.status-dot {
		width: 16rpx;
		height: 16rpx;
		border-radius: var(--radius-full);
		display: inline-block;
		margin-right: var(--space-xs);
	}

	.status-dot.online {
		background-color: var(--success-color);
		box-shadow: 0 0 0 4rpx var(--success-bg);
	}

	.status-dot.offline {
		background-color: var(--gray-400);
		box-shadow: 0 0 0 4rpx var(--gray-100);
	}

	.status-dot.busy {
		background-color: var(--warning-color);
		box-shadow: 0 0 0 4rpx var(--warning-bg);
	}

	.status-dot.error {
		background-color: var(--error-color);
		box-shadow: 0 0 0 4rpx var(--error-bg);
	}
</style>