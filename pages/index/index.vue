<template>
	<view>
		<task v-if="PageCur=='task'"></task>
		<customer v-if="PageCur=='customer'"></customer>
		<setting v-if="PageCur=='setting'"></setting>
		<agent v-if="PageCur=='agent'"></agent>
		<view class="modern-tabbar">
			<view class="modern-tab-item" :class="{'active': PageCur=='task'}" @click="NavChange" data-cur="task">
				<view class="tab-icon-wrapper">
					<image class="tab-icon" :src="'/static/tabbar/task' + [PageCur=='task'?'_cur':''] + '.png'"></image>
					<view class="tab-indicator" v-if="PageCur=='task'"></view>
				</view>
				<view class="tab-label" :class="PageCur=='task'?'text-primary':'text-tertiary'">任务</view>
			</view>
			<view class="modern-tab-item" :class="{'active': PageCur=='customer'}" @click="NavChange" data-cur="customer">
				<view class="tab-icon-wrapper">
					<image class="tab-icon" :src="'/static/tabbar/customer' + [PageCur == 'customer'?'_cur':''] + '.png'"></image>
					<view class="tab-indicator" v-if="PageCur=='customer'"></view>
				</view>
				<view class="tab-label" :class="PageCur=='customer'?'text-primary':'text-tertiary'">客户</view>
			</view>
			<view class="modern-tab-item" :class="{'active': PageCur=='agent'}" @click="NavChange" data-cur="agent">
				<view class="tab-icon-wrapper">
					<image class="tab-icon" :src="'/static/tabbar/agent' + [PageCur == 'agent'?'_cur':''] + '.png'"></image>
					<view class="tab-indicator" v-if="PageCur=='agent'"></view>
				</view>
				<view class="tab-label" :class="PageCur=='agent'?'text-primary':'text-tertiary'">坐席</view>
			</view>
			<view class="modern-tab-item" :class="{'active': PageCur=='setting'}" @click="NavChange" data-cur="setting">
				<view class="tab-icon-wrapper">
					<image class="tab-icon" :src="'/static/tabbar/setting' + [PageCur == 'setting'?'_cur':''] + '.png'"></image>
					<view class="tab-indicator" v-if="PageCur=='setting'"></view>
				</view>
				<view class="tab-label" :class="PageCur=='setting'?'text-primary':'text-tertiary'">设置</view>
			</view>
		</view>
	</view>
</template>

<script>
	import {
		initWs, reConnect, closeConnect
	} from '../../utils/useSocket.js'
	export default {
		data() {
			return {
				PageCur: 'task'
			}
		},
		onShow() {
			// this.checkToken()
			uni.getStorage({
				key: 'userInfo',
				success: (res)=> {
					console.log('app res ',res)
					this.globalData.userInfo = res.data
				},
				fail: function(err) {
					// that.getVerificationCode()
				}
			});
			if (this.isHide) {
				reConnect('show')
			} else {
				initWs()
			}
		},
		methods: {
			NavChange: function(e) {
				this.PageCur = e.currentTarget.dataset.cur
			}
		}
	}
</script>

<style scoped>
/* 现代化底部标签栏 */
.modern-tabbar {
	position: fixed;
	bottom: 0;
	left: 0;
	right: 0;
	height: 120rpx;
	background: rgba(255, 255, 255, 0.95);
	backdrop-filter: blur(20rpx);
	border-top: 1rpx solid var(--border-color-light, #F0F0F0);
	display: flex;
	align-items: center;
	justify-content: space-around;
	padding: 0 24rpx;
	box-shadow: 0 -4rpx 24rpx rgba(0, 0, 0, 0.08);
	z-index: 999;
	transition: all 0.3s ease;
}

/* 标签项 */
.modern-tab-item {
	flex: 1;
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
	padding: 16rpx 8rpx;
	position: relative;
	transition: all 0.2s ease;
	border-radius: var(--radius-md, 12rpx);
}

.modern-tab-item:active {
	transform: scale(0.95);
	background: var(--bg-tertiary, #F5F5F5);
}

.modern-tab-item.active {
	background: linear-gradient(135deg, rgba(24, 144, 255, 0.1) 0%, rgba(64, 169, 255, 0.05) 100%);
}

/* 图标容器 */
.tab-icon-wrapper {
	position: relative;
	width: 48rpx;
	height: 48rpx;
	display: flex;
	align-items: center;
	justify-content: center;
	margin-bottom: 8rpx;
}

/* 图标 */
.tab-icon {
	width: 48rpx;
	height: 48rpx;
	transition: all 0.2s ease;
}

.modern-tab-item.active .tab-icon {
	transform: scale(1.1);
}

/* 活跃指示器 */
.tab-indicator {
	position: absolute;
	top: -4rpx;
	right: -4rpx;
	width: 16rpx;
	height: 16rpx;
	background: linear-gradient(135deg, var(--primary-color, #1890FF) 0%, var(--primary-light, #40A9FF) 100%);
	border-radius: 50%;
	box-shadow: 0 2rpx 8rpx rgba(24, 144, 255, 0.4);
	animation: pulse 2s infinite;
}

/* 标签文字 */
.tab-label {
	font-size: 22rpx;
	font-weight: 500;
	transition: all 0.2s ease;
	text-align: center;
	line-height: 1.2;
}

.modern-tab-item.active .tab-label {
	font-weight: 600;
	transform: scale(1.05);
}

/* 脉冲动画 */
@keyframes pulse {
	0% {
		transform: scale(1);
		opacity: 1;
	}
	50% {
		transform: scale(1.2);
		opacity: 0.7;
	}
	100% {
		transform: scale(1);
		opacity: 1;
	}
}

/* 深色模式支持 */
@media (prefers-color-scheme: dark) {
	.modern-tabbar {
		background: rgba(26, 26, 26, 0.95);
		border-top-color: rgba(255, 255, 255, 0.1);
	}

	.modern-tab-item:active {
		background: rgba(255, 255, 255, 0.1);
	}

	.modern-tab-item.active {
		background: linear-gradient(135deg, rgba(24, 144, 255, 0.2) 0%, rgba(64, 169, 255, 0.1) 100%);
	}
}

/* 安全区域适配 */
@supports (bottom: env(safe-area-inset-bottom)) {
	.modern-tabbar {
		padding-bottom: env(safe-area-inset-bottom);
		height: calc(120rpx + env(safe-area-inset-bottom));
	}
}

/* 响应式适配 */
@media (max-width: 750rpx) {
	.modern-tabbar {
		height: 100rpx;
		padding: 0 16rpx;
	}

	.modern-tab-item {
		padding: 12rpx 4rpx;
	}

	.tab-icon-wrapper {
		width: 40rpx;
		height: 40rpx;
		margin-bottom: 6rpx;
	}

	.tab-icon {
		width: 40rpx;
		height: 40rpx;
	}

	.tab-label {
		font-size: 20rpx;
	}
}
</style>