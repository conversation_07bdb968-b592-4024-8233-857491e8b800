<template>
	<view class="login-container">
		<!-- 背景装饰 -->
		<view class="background-decoration">
			<view class="wave-shape"></view>
		</view>

		<!-- 主要内容 -->
		<view class="main-content">
			<!-- 顶部品牌区域 -->
			<view class="brand-section">
				<!-- <image src="/logo/logo.png" class="brand-logo" mode="aspectFit"></image>
				<view class="brand-title">翼呼</view> -->
			</view>

			<!-- 登录卡片 -->
			<view class="login-card">
				<view class="card-header">
					<text class="login-title">欢迎回来</text>
					<!-- <text class="login-subtitle">请登录您的账户</text> -->
				</view>

				<view class="form-section">
					<!-- 用户名输入框 -->
					<view class="input-wrapper">
						<view class="input-icon">👤</view>
						<input
							class="form-input"
							type="text"
							placeholder="用户名@租户"
							@input="userPhone_input"
							@focus="onUsernameFocus"
							@blur="onUsernameBlur"
							:value="account.username"
							maxlength="50"
						/>
						<view class="clear-icon" v-if="account.username && usernameFocused" @tap="clearUsername">
							<text class="clear-text">✕</text>
						</view>
					</view>

					<!-- 密码输入框 -->
					<view class="input-wrapper">
						<view class="input-icon">🔒</view>
						<input
							class="form-input"
							type="password"
							placeholder="密码"
							@input="pwd_input"
							@focus="onPasswordFocus"
							@blur="onPasswordBlur"
							:value="account.password"
							maxlength="50"
						/>
						<view class="clear-icon" v-if="account.password && passwordFocused" @tap="clearPassword">
							<text class="clear-text">✕</text>
						</view>
					</view>

					<!-- 记住密码选项 -->
					<view class="remember-section">
						<view class="checkbox-wrapper" @tap="toggleRememberPassword">
							<view class="checkbox" :class="rememberPassword ? 'checked' : ''">
								<text class="check-icon" v-if="rememberPassword">✓</text>
							</view>
							<text class="checkbox-label">记住密码</text>
						</view>
					</view>

					<button class="login-btn" @click="login_click">
						<text class="btn-text">立即登录</text>
					</button>
				</view>

				<!-- <view class="card-footer">
					<text class="footer-text">首次使用？</text>
					<text class="link-text">联系管理员</text>
				</view> -->
			</view>
		</view>
	</view>
</template>

<script>
	import {
		encryptEnc
	} from '../../utils/cryptoUtil.js'
	export default {
		data() {
			return {
				account: {
					"username": "",
					"password": "",
					"accountId": "",
					"verifyCode": "",
					"signature": ""
				},
				verificationCode: "",
				rememberPassword: false,  // 记住密码状态
				usernameFocused: false,   // 用户名输入框焦点状态
				passwordFocused: false    // 密码输入框焦点状态
			}
		},
		onLoad: function() {
			var that = this
			const token = uni.getStorageSync('token');
			if (token) {
				uni.getStorage({
					key: 'userInfo',
					success: function(res) {
						that.globalData.userInfo = res.data
						that.account = res.data
						uni.reLaunch({
							url: '/pages/index/index'
						});
					},
					fail: function(err) {
						// that.getVerificationCode()
					}
				});
			} else {
				// 读取记住的密码
				that.loadRememberedCredentials();
			}
		},
		methods: {
			// 读取记住的用户凭据
			loadRememberedCredentials() {
				try {
					const rememberedData = uni.getStorageSync('rememberedCredentials');
					if (rememberedData) {
						this.account.username = rememberedData.username || '';
						this.account.password = rememberedData.password || '';
						this.rememberPassword = true;
					}
				} catch (e) {
					console.log('读取记住的密码失败:', e);
				}
			},

			// 保存记住的用户凭据
			saveRememberedCredentials() {
				if (this.rememberPassword) {
					try {
						const credentialsData = {
							username: this.account.username,
							password: this.account.password
						};
						uni.setStorageSync('rememberedCredentials', credentialsData);
					} catch (e) {
						console.log('保存记住的密码失败:', e);
					}
				} else {
					// 如果不记住密码，清除已保存的凭据
					try {
						uni.removeStorageSync('rememberedCredentials');
					} catch (e) {
						console.log('清除记住的密码失败:', e);
					}
				}
			},

			// 切换记住密码状态
			toggleRememberPassword() {
				this.rememberPassword = !this.rememberPassword;
			},

			// 清空用户名
			clearUsername() {
				this.account.username = '';
			},

			// 清空密码
			clearPassword() {
				this.account.password = '';
			},

			// 用户名输入框获得焦点
			onUsernameFocus() {
				this.usernameFocused = true;
			},

			// 用户名输入框失去焦点
			onUsernameBlur() {
				// 延迟隐藏，避免点击清空按钮时过早隐藏
				setTimeout(() => {
					this.usernameFocused = false;
				}, 150);
			},

			// 密码输入框获得焦点
			onPasswordFocus() {
				this.passwordFocused = true;
			},

			// 密码输入框失去焦点
			onPasswordBlur() {
				// 延迟隐藏，避免点击清空按钮时过早隐藏
				setTimeout(() => {
					this.passwordFocused = false;
				}, 150);
			},

			getVerificationCode(e) {
				var that = this
				this.httpGet("/conf/rest/verify/code").then(res => {
					if (res.data.code == 1) {
						that.verificationCode = res.data.imgInfo
						that.account.signature = res.data.signature
					} else {
						this.showText('获取验证码失败');
					}
				}).catch(err => {
					this.showText('服务器异常');
				})
			},
			userPhone_input(e) {
				this.account.username = e.target.value
			},
			pwd_input(e) {
				this.account.password = e.target.value
			},
			code_input(e) {
				this.account.verifyCode = e.target.value
			},
			checkToken(data) {
				var that = this
				uni.getStorage({
					key: 'userInfo',
					success: function(res) {
						console.log(res)
					},
					fail: function(err) {
						that.setToken('userInfo', data)
					},
					complete: function() {}
				});
			},
			login_click(e) {
				var data = this.account
				if (!data.username.trim()) {
					this.showText('请输入用户名');
					return;
				}
				if (!data.password.trim()) {
					this.showText('请输入密码');
					return;
				}

				// 保存原始密码用于记住密码功能
				const originalPassword = data.password;

				// 加密密码
				data.password = encryptEnc(data.password)

				this.httpPost('/server/agent/login', data).then(res => {
					console.log('login ', res.data)
					if (res.data.code == 500) {
						this.showText(res.data.message);
						return
					}

					// 登录成功，保存记住的密码
					if (this.rememberPassword) {
						// 恢复原始密码用于保存
						this.account.password = originalPassword;
						this.saveRememberedCredentials();
					} else {
						// 清除记住的密码
						this.saveRememberedCredentials();
					}

					let userInfo = res.data.data
					this.setToken('token', res.data.message)
					this.setToken('userInfo', userInfo)
					this.globalData.userInfo = userInfo
					uni.reLaunch({
						url: '/pages/index/index'
					});
				}).catch(err => {
					console.log(err)
					this.showText('服务器异常');
				})
			}
		}
	}
</script>
<style scoped>
/* 登录页面容器 */
.login-container {
	min-height: 100vh;
	background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
	position: relative;
	overflow: hidden;
}

/* 背景装饰 */
.background-decoration {
	position: absolute;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	z-index: 1;
}

.wave-shape {
	position: absolute;
	bottom: 0;
	left: 0;
	right: 0;
	height: 200rpx;
	background: rgba(255, 255, 255, 0.1);
	border-radius: 50% 50% 0 0;
	transform: scaleX(2);
}

/* 主要内容 */
.main-content {
	position: relative;
	z-index: 2;
	min-height: 100vh;
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
	padding: 40rpx;
}

/* 品牌区域 */
.brand-section {
	display: flex;
	align-items: center;
	justify-content: center;
	margin-bottom: 60rpx;
}

.brand-logo {
	width: 80rpx;
	height: 80rpx;
	margin-right: 20rpx;
}

.brand-title {
	font-size: 72rpx;
	font-weight: 700;
	color: #FFFFFF;
	letter-spacing: 4rpx;
}

/* 登录卡片 */
.login-card {
	width: 100%;
	max-width: 600rpx;
	background: #FFFFFF;
	border-radius: 32rpx;
	padding: 60rpx 48rpx;
	box-shadow: 0 20rpx 60rpx rgba(0, 0, 0, 0.1);
	backdrop-filter: blur(20rpx);
}

/* 卡片头部 */
.card-header {
	text-align: center;
	margin-bottom: 60rpx;
}

.login-title {
	font-size: 48rpx;
	font-weight: 600;
	color: #333333;
	margin-bottom: 12rpx;
	display: block;
}

.login-subtitle {
	font-size: 28rpx;
	color: #666666;
	display: block;
}

/* 表单区域 */
.form-section {
	margin-bottom: 40rpx;
}

/* 输入框包装器 */
.input-wrapper {
	position: relative;
	margin-bottom: 32rpx;
	display: flex;
	align-items: center;
	background: #F8F9FA;
	border-radius: 16rpx;
	padding: 0 24rpx;
	height: 100rpx;
}

.input-icon {
	font-size: 32rpx;
	margin-right: 16rpx;
	color: #999999;
}

.form-input {
	flex: 1;
	height: 100%;
	border: none;
	background: transparent;
	font-size: 30rpx;
	color: #333333;
	outline: none;
}

.form-input::placeholder {
	color: #CCCCCC;
	font-size: 28rpx;
}

/* 清空按钮 */
.clear-icon {
	padding: 8rpx;
	margin-left: 8rpx;
	display: flex;
	align-items: center;
	justify-content: center;
	cursor: pointer;
	opacity: 1;
	transition: all 0.2s ease;
	border-radius: 50%;
	width: 40rpx;
	height: 40rpx;
}

.clear-icon:hover {
	background-color: rgba(0, 0, 0, 0.05);
}

.clear-text {
	font-size: 24rpx;
	color: #CCCCCC;
	font-weight: bold;
	transition: all 0.2s ease;
	line-height: 1;
}

.clear-icon:active {
	transform: scale(0.9);
	background-color: rgba(0, 0, 0, 0.1);
}

.clear-icon:active .clear-text {
	color: #999999;
}

/* 记住密码区域 */
.remember-section {
	margin: 24rpx 0;
	display: flex;
	align-items: center;
}

.checkbox-wrapper {
	display: flex;
	align-items: center;
	cursor: pointer;
}

.checkbox {
	width: 36rpx;
	height: 36rpx;
	border: 2rpx solid #CCCCCC;
	border-radius: 6rpx;
	display: flex;
	align-items: center;
	justify-content: center;
	margin-right: 16rpx;
	transition: all 0.3s ease;
}

.checkbox.checked {
	background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
	border-color: #667eea;
}

.check-icon {
	font-size: 20rpx;
	color: #FFFFFF;
	font-weight: bold;
}

.checkbox-label {
	font-size: 28rpx;
	color: #666666;
	user-select: none;
}

/* 登录按钮 */
.login-btn {
	width: 100%;
	height: 100rpx;
	background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
	border-radius: 16rpx;
	border: none;
	margin-top: 40rpx;
	display: flex;
	align-items: center;
	justify-content: center;
	box-shadow: 0 8rpx 24rpx rgba(102, 126, 234, 0.3);
	transition: all 0.3s ease;
}

.login-btn:active {
	transform: translateY(2rpx);
	box-shadow: 0 4rpx 16rpx rgba(102, 126, 234, 0.4);
}

.btn-text {
	font-size: 32rpx;
	font-weight: 600;
	color: #FFFFFF;
}

/* 卡片底部 */
.card-footer {
	text-align: center;
	display: flex;
	align-items: center;
	justify-content: center;
}

.footer-text {
	font-size: 26rpx;
	color: #999999;
	margin-right: 8rpx;
}

.link-text {
	font-size: 26rpx;
	color: #667eea;
	font-weight: 500;
}




/* 响应式适配 */
@media (max-width: 750rpx) {
	.main-content {
		padding: 60rpx 32rpx;
	}

	.brand-icon {
		font-size: 100rpx;
	}

	.brand-title {
		font-size: 60rpx;
		letter-spacing: 3rpx;
	}

	.brand-subtitle {
		font-size: 26rpx;
	}

	.login-card {
		max-width: 650rpx;
		padding: 50rpx 40rpx;
	}

	.login-title {
		font-size: 42rpx;
	}

	.login-subtitle {
		font-size: 26rpx;
	}

	.input-wrapper {
		height: 96rpx;
		padding: 0 20rpx;
	}

	.form-input {
		font-size: 28rpx;
	}

	.clear-text {
		font-size: 22rpx;
	}

	.checkbox {
		width: 32rpx;
		height: 32rpx;
	}

	.check-icon {
		font-size: 18rpx;
	}

	.checkbox-label {
		font-size: 26rpx;
	}

	.login-btn {
		height: 96rpx;
	}

	.btn-text {
		font-size: 30rpx;
	}
}

/* 超小屏幕适配 */
@media (max-width: 600rpx) {
	.main-content {
		padding: 24rpx;
	}

	.brand-section {
		margin-bottom: 40rpx;
	}

	.brand-logo {
		width: 60rpx;
		height: 60rpx;
		margin-right: 12rpx;
	}

	.brand-title {
		font-size: 52rpx;
		letter-spacing: 2rpx;
	}

	.login-card {
		padding: 40rpx 32rpx;
	}

	.login-title {
		font-size: 38rpx;
	}

	.login-subtitle {
		font-size: 24rpx;
	}

	.input-wrapper {
		height: 92rpx;
		margin-bottom: 28rpx;
		padding: 0 18rpx;
	}

	.input-icon {
		font-size: 28rpx;
		margin-right: 12rpx;
	}

	.form-input {
		font-size: 26rpx;
	}

	.clear-text {
		font-size: 20rpx;
	}

	.checkbox {
		width: 30rpx;
		height: 30rpx;
	}

	.check-icon {
		font-size: 16rpx;
	}

	.checkbox-label {
		font-size: 24rpx;
	}

	.login-btn {
		height: 92rpx;
		margin-top: 32rpx;
	}

	.btn-text {
		font-size: 28rpx;
	}

	.footer-text, .link-text {
		font-size: 24rpx;
	}
}
</style>