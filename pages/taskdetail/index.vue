	<template>
		<view class="task-detail-container">
			<!-- 自定义导航栏 -->
			<cu-custom bgColor="bg-gradual-blue" :isBack="true">
				<block slot="backText">返回</block>
				<block slot="content">任务详情</block>
			</cu-custom>

			<!-- 搜索栏 -->
			<view class="search-section" :style="[{top: CustomBar + 'px'}]">
				<view class="search-wrapper">
					<view class="search-input-wrapper">
						<text class="search-icon cuIcon-search"></text>
						<input
							class="search-input"
							type="text"
							placeholder="输入搜索的关键词"
							confirm-type="search"
							v-model="searchKeyword"
							@input="onSearchInput"
						/>
						<text class="clear-icon cuIcon-close" v-if="searchKeyword" @tap="clearSearch"></text>
					</view>
					<button class="search-btn" @tap="performSearch">
						<text class="search-btn-text">搜索</text>
					</button>
				</view>
			</view>

			<!-- 任务统计信息 -->
			<view class="stats-section" v-if="taskDetail.length > 0">
				<view class="stats-card">
					<view class="stats-item">
						<text class="stats-number">{{myCustomerCount}}</text>
						<text class="stats-label">我的客户</text>
					</view>
					<view class="stats-divider"></view>
					<view class="stats-item">
						<text class="stats-number">{{calledCount}}</text>
						<text class="stats-label">已拨打</text>
					</view>
				</view>
			</view>

			<!-- 客户列表 -->
			<scroll-view
				scroll-y
				class="customer-list"
				:style="[{height: 'calc(100vh - ' + CustomBar + 'px - 200px)'}]"
				:scroll-with-animation="true"
				:enable-back-to-top="true"
			>
				<view class="list-container">
					<view
						class="customer-card"
						v-for="(item, index) in filteredTaskDetail"
						:key="index"
						:data-id="index"
					>
						<view class="customer-info">
							<view class="customer-header">
								<view class="customer-name">
									<text class="name-label">客户：</text>
									<text class="name-value">{{item.name || '未知客户'}}</text>
								</view>
								<view class="call-status" :class="getStatusClass(item.status)">
									<text class="status-text">{{getStatusText(item.status)}}</text>
								</view>
							</view>
							<view class="customer-phone">
								<text class="phone-label">联系方式：</text>
								<text class="phone-value">{{item.telephone || '暂无电话'}}</text>
							</view>
							<view class="customer-meta" v-if="item.businessCode">
								<text class="meta-text">业务代码：{{item.businessCode}}</text>
							</view>
						</view>
						<view class="action-section">
							<button
								class="call-btn"
								:class="item.calling ? 'calling' : ''"
								:disabled="item.calling || !item.telephone"
								@tap="makecall(index)"
							>
								<text class="call-icon cuIcon-phone" v-if="!item.calling"></text>
								<text class="loading-icon cuIcon-loading2 cuIconfont-spin" v-if="item.calling"></text>
								<text class="call-text">{{item.calling ? '拨打中' : '拨号'}}</text>
							</button>
						</view>
					</view>

					<!-- 空状态 -->
					<view class="empty-state" v-if="filteredTaskDetail.length === 0 && !loading">
						<text class="empty-icon cuIcon-infofill"></text>
						<text class="empty-text">{{searchKeyword ? '未找到相关客户' : '暂无客户数据'}}</text>
					</view>

					<!-- 加载状态 -->
					<view class="loading-state" v-if="loading">
						<text class="loading-icon cuIcon-loading2 cuIconfont-spin"></text>
						<text class="loading-text">加载中...</text>
					</view>

					<!-- 底部安全区域 -->
					<view class="bottom-safe-area"></view>
				</view>
			</scroll-view>
		</view>
	</template>

	<script>
		export default {
			data() {
				return {
					CustomBar: this.CustomBar,
					taskId: 0,
					taskDetail: [],
					searchKeyword: '',
					loading: false,
					calledCount: 0
				};
			},
			computed: {
				// 过滤后的任务详情列表（只显示当前坐席的客户）
				filteredTaskDetail() {
					if (!this.globalData || !this.globalData.userInfo) {
						return [];
					}

					const currentAgentNum = this.globalData.userInfo.agentNum;

					// 先过滤出当前坐席的客户
					let myCustomers = this.taskDetail.filter(item =>
						item.agentNum === currentAgentNum
					);

					// 再根据搜索关键词过滤
					if (!this.searchKeyword.trim()) {
						return myCustomers;
					}

					const keyword = this.searchKeyword.toLowerCase();
					return myCustomers.filter(item => {
						return (item.name && item.name.toLowerCase().includes(keyword)) ||
							   (item.telephone && item.telephone.includes(keyword));
					});
				},

				// 当前坐席的客户数量
				myCustomerCount() {
					if (!this.globalData || !this.globalData.userInfo) {
						return 0;
					}
					const currentAgentNum = this.globalData.userInfo.agentNum;
					return this.taskDetail.filter(item =>
						item.agentNum === currentAgentNum
					).length;
				}
			},
			onLoad(option) {
				console.log(option.id);
				this.taskId = option.id
			},
			onUnload() {
				// 页面卸载时通知任务列表刷新
				uni.$emit('refreshTaskList');
			},
			created() {
				console.log('task show')
				console.log('CustomBar ', this.CustomBar)
				this.getTaskDetail()
			},
			onReady() {
				let that = this;
				uni.createSelectorQuery().select('.customer-list').boundingClientRect(function(res) {
					that.barTop = res.top
				}).exec()
			},
			methods: {
				// 拨号功能
				makecall(val) {
					const detail = this.filteredTaskDetail[val];
					const originalIndex = this.taskDetail.findIndex(item => item.id === detail.id);

					if (!detail.telephone) {
						uni.showToast({
							title: '该客户暂无电话号码',
							icon: 'none',
							duration: 2000
						});
						return;
					}

					// 显示拨号确认弹窗
					uni.showModal({
						title: '确认拨号',
						content: `即将拨打客户 ${detail.name || '未知客户'} 的电话：${detail.telephone}`,
						confirmText: '立即拨打',
						cancelText: '取消',
						success: (res) => {
							if (res.confirm) {
								this.performCall(detail, originalIndex);
							}
						}
					});
				},

				// 执行拨号
				performCall(detail, originalIndex) {
					// 设置拨打状态
					this.$set(this.taskDetail[originalIndex], 'calling', true);

					let req = {
						domain: detail.domain,
						businessCode: detail.businessCode,
						callerNum: this.globalData.userInfo.phone,
						calleeNum: detail.telephone,
						uui: 'preview_outbound_app_' + detail.id,
						authCode: "Get5pJnR"
					}

					let sign = this.createSign(req, "dN9L02Fx0x98o7dGRc7nMgNHA8uPB9iT");
					console.log("Generated Sign:", sign);
					req.sign = sign;

					this.httpPost("/call/yihu-outbound-unit/outboundUnit/mobileCall", req).then(res => {
						console.log('makecall resp ', res);

						// 重置拨打状态
						this.$set(this.taskDetail[originalIndex], 'calling', false);

						if (res.data.code == 200) {
							// 更新状态为已拨打，并设置当前坐席号
							this.$set(this.taskDetail[originalIndex], 'status', 1);
							this.$set(this.taskDetail[originalIndex], 'agentNum', this.globalData.userInfo.agentNum);
							this.updateCalledCount();

							uni.showToast({
								title: '拨打成功',
								icon: 'success',
								duration: 2000
							});
						} else {
							uni.showToast({
								title: res.data.message || '拨打失败',
								icon: 'none',
								duration: 2000
							});
						}
					}).catch(err => {
						console.log(err);
						// 重置拨打状态
						this.$set(this.taskDetail[originalIndex], 'calling', false);

						uni.showToast({
							title: '网络异常，请稍后重试',
							icon: 'none',
							duration: 2000
						});
					});
				},

				// 搜索输入处理
				onSearchInput(e) {
					this.searchKeyword = e.detail.value;
				},

				// 执行搜索
				performSearch() {
					if (!this.searchKeyword.trim()) {
						uni.showToast({
							title: '请输入搜索关键词',
							icon: 'none',
							duration: 1500
						});
						return;
					}
					// 搜索逻辑已在computed中实现
					uni.showToast({
						title: `找到 ${this.filteredTaskDetail.length} 条结果`,
						icon: 'none',
						duration: 1500
					});
				},

				// 清除搜索
				clearSearch() {
					this.searchKeyword = '';
				},

				// 获取状态样式类
				getStatusClass(status) {
					const statusMap = {
						0: 'uncalled',    // 未拨打
						1: 'called',      // 已拨打
						2: 'answered'     // 已接听
					}
					return statusMap[status] || 'uncalled'
				},

				// 获取状态文本
				getStatusText(status) {
					const statusMap = {
						0: '未拨打',
						1: '已拨打',
						2: '已接听'
					}
					return statusMap[status] || '未拨打'
				},

				// 更新已拨打数量（只计算当前坐席的）
				updateCalledCount() {
					const currentAgentNum = this.globalData.userInfo.agentNum;
					this.calledCount = this.taskDetail.filter(item =>
						item.agentNum === currentAgentNum && (item.status === 1 || item.status === 2)
					).length;
				},
				// 获取任务详情
				getTaskDetail() {
					this.loading = true;

					let req = {
						"startPage": 1,
						"pageNum": 100,
						"previewTaskId": this.taskId
					}

					this.httpPost("/server/crm-preview-outbound//preview-outbound/previewCustomer/queryPageList", req).then(
						res => {
							if (res.data && res.data.data && res.data.data.pageData) {
								// 初始化每个客户的状态，保留原有的status字段
								this.taskDetail = res.data.data.pageData.map(item => ({
									...item,
									calling: false  // 只添加拨打中状态
								}));
								this.updateCalledCount();
							} else {
								this.taskDetail = [];
							}
							this.loading = false;
						}).catch(err => {
						console.log('获取任务详情失败:', err);
						this.loading = false;
						uni.showToast({
							title: '获取数据失败，请重试',
							icon: 'none',
							duration: 2000
						});
					})
				}
			}
		}
	</script>

	<style scoped>
		/* 页面容器 */
		.task-detail-container {
			background: #f5f7fa;
			min-height: 100vh;
		}

		/* 搜索区域 */
		.search-section {
			position: fixed;
			left: 0;
			right: 0;
			z-index: 999;
			background: #ffffff;
			padding: 20rpx;
			box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.1);
		}

		.search-wrapper {
			display: flex;
			align-items: center;
			gap: 20rpx;
		}

		.search-input-wrapper {
			flex: 1;
			position: relative;
			background: #f8f9fa;
			border-radius: 50rpx;
			padding: 0 40rpx;
			height: 80rpx;
			display: flex;
			align-items: center;
		}

		.search-icon {
			color: #999999;
			font-size: 32rpx;
			margin-right: 20rpx;
		}

		.search-input {
			flex: 1;
			height: 100%;
			border: none;
			background: transparent;
			font-size: 28rpx;
			color: #333333;
		}

		.clear-icon {
			color: #cccccc;
			font-size: 28rpx;
			padding: 10rpx;
		}

		.search-btn {
			background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
			color: #ffffff;
			border: none;
			border-radius: 40rpx;
			padding: 0 32rpx;
			height: 80rpx;
			font-size: 28rpx;
			box-shadow: 0 4rpx 12rpx rgba(102, 126, 234, 0.3);
		}

		.search-btn-text {
			color: #ffffff;
			font-size: 28rpx;
			font-weight: 500;
		}

		/* 统计区域 */
		.stats-section {
			margin-top: 140rpx;
			padding: 20rpx;
		}

		.stats-card {
			background: #ffffff;
			border-radius: 20rpx;
			padding: 30rpx;
			display: flex;
			align-items: center;
			justify-content: space-around;
			box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);
		}

		.stats-item {
			text-align: center;
		}

		.stats-number {
			display: block;
			font-size: 48rpx;
			font-weight: 700;
			color: #667eea;
			margin-bottom: 8rpx;
		}

		.stats-label {
			font-size: 24rpx;
			color: #999999;
		}

		.stats-divider {
			width: 2rpx;
			height: 60rpx;
			background: #e5e5e5;
		}

		/* 客户列表 */
		.customer-list {
			padding-top: 20rpx;
		}

		.list-container {
			padding: 0 20rpx 40rpx;
		}

		/* 底部安全区域 */
		.bottom-safe-area {
			height: calc(120rpx + env(safe-area-inset-bottom)); /* tabbar高度 + 底部安全区域 */
			background: transparent;
		}

		.customer-card {
			background: #ffffff;
			border-radius: 20rpx;
			margin-bottom: 20rpx;
			padding: 30rpx;
			display: flex;
			align-items: center;
			justify-content: space-between;
			box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.06);
			transition: all 0.3s ease;
		}

		.customer-card:active {
			transform: scale(0.98);
		}

		.customer-info {
			flex: 1;
			margin-right: 20rpx;
		}

		.customer-header {
			display: flex;
			align-items: center;
			justify-content: space-between;
			margin-bottom: 16rpx;
		}

		.customer-name {
			display: flex;
			align-items: center;
		}

		.name-label {
			font-size: 26rpx;
			color: #999999;
			margin-right: 8rpx;
		}

		.name-value {
			font-size: 32rpx;
			font-weight: 600;
			color: #333333;
		}

		.call-status {
			padding: 8rpx 16rpx;
			border-radius: 20rpx;
			font-size: 22rpx;
		}

		.call-status.called {
			background: #e8f5e8;
			color: #52c41a;
		}

		.call-status.uncalled {
			background: #fff2e8;
			color: #fa8c16;
		}

		.call-status.answered {
			background: #e6f7ff;
			color: #1890ff;
		}

		.status-text {
			font-size: 22rpx;
			font-weight: 500;
		}

		.customer-phone {
			display: flex;
			align-items: center;
			margin-bottom: 12rpx;
		}

		.phone-label {
			font-size: 26rpx;
			color: #999999;
			margin-right: 8rpx;
		}

		.phone-value {
			font-size: 28rpx;
			color: #666666;
			font-family: 'Courier New', monospace;
		}

		.customer-meta {
			margin-top: 8rpx;
		}

		.meta-text {
			font-size: 24rpx;
			color: #cccccc;
		}

		/* 操作区域 */
		.action-section {
			display: flex;
			align-items: center;
		}

		.call-btn {
			background: linear-gradient(135deg, #52c41a 0%, #73d13d 100%);
			color: #ffffff;
			border: none;
			border-radius: 50rpx;
			padding: 0 32rpx;
			height: 80rpx;
			display: flex;
			align-items: center;
			justify-content: center;
			gap: 12rpx;
			box-shadow: 0 4rpx 12rpx rgba(82, 196, 26, 0.3);
			transition: all 0.3s ease;
			min-width: 140rpx;
		}

		.call-btn:disabled {
			background: #f5f5f5;
			color: #cccccc;
			box-shadow: none;
		}

		.call-btn.calling {
			background: linear-gradient(135deg, #faad14 0%, #ffc53d 100%);
			box-shadow: 0 4rpx 12rpx rgba(250, 173, 20, 0.3);
		}

		.call-btn:active:not(:disabled) {
			transform: scale(0.95);
		}

		.call-icon {
			font-size: 28rpx;
		}

		.loading-icon {
			font-size: 28rpx;
		}

		.call-text {
			font-size: 26rpx;
			font-weight: 500;
		}

		/* 空状态 */
		.empty-state {
			text-align: center;
			padding: 120rpx 40rpx;
		}

		.empty-icon {
			font-size: 120rpx;
			color: #e5e5e5;
			margin-bottom: 30rpx;
			display: block;
		}

		.empty-text {
			font-size: 28rpx;
			color: #999999;
		}

		/* 加载状态 */
		.loading-state {
			text-align: center;
			padding: 80rpx 40rpx;
			display: flex;
			flex-direction: column;
			align-items: center;
		}

		.loading-state .loading-icon {
			font-size: 60rpx;
			color: #667eea;
			margin-bottom: 20rpx;
		}

		.loading-text {
			font-size: 28rpx;
			color: #999999;
		}

		/* 动画效果 */
		@keyframes spin {
			0% { transform: rotate(0deg); }
			100% { transform: rotate(360deg); }
		}

		.cuIconfont-spin {
			animation: spin 1s linear infinite;
		}

		/* 响应式适配 */
		@media (max-width: 750rpx) {
			.search-section {
				padding: 16rpx;
			}

			.search-input-wrapper {
				height: 72rpx;
				padding: 0 32rpx;
			}

			.search-btn {
				height: 72rpx;
				padding: 0 24rpx;
			}

			.stats-section {
				margin-top: 120rpx;
				padding: 16rpx;
			}

			.stats-card {
				padding: 24rpx;
			}

			.stats-number {
				font-size: 40rpx;
			}

			.customer-card {
				padding: 24rpx;
				margin-bottom: 16rpx;
			}

			.name-value {
				font-size: 28rpx;
			}

			.call-btn {
				height: 72rpx;
				padding: 0 24rpx;
				min-width: 120rpx;
			}
		}

		@media (max-width: 600rpx) {
			.search-input-wrapper {
				height: 68rpx;
				padding: 0 28rpx;
			}

			.search-btn {
				height: 68rpx;
				padding: 0 20rpx;
			}

			.stats-card {
				padding: 20rpx;
			}

			.stats-number {
				font-size: 36rpx;
			}

			.customer-card {
				padding: 20rpx;
			}

			.call-btn {
				height: 68rpx;
				padding: 0 20rpx;
				min-width: 100rpx;
			}

			.call-text {
				font-size: 24rpx;
			}
		}
	</style>