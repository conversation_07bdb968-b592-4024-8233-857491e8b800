<template>
	<view class="position-r">
		<view>
			<!-- <cu-custom bgColor="bg-gray light">
				<block slot="content">
					<text>坐席工号:</text>
					<text class="text-blue">{{message.agentId}}</text>
				</block>
				<block slot="right">
					<view class="action margin-left-auto" @tap="signOut" data-target="signOutModal">
						<view :class="(errText || ['通话中'].includes(stateText)) ? 'text-gray' : ''" class="icon-logOut" style="width: 40rpx; height: 40rpx; font-size: 40rpx;"></view>
					</view>
				</block>
			</cu-custom> -->
			<cu-custom bgColor="bg-gradual-blue" :isBack="false">
				<block slot="content">远程坐席</block>
			</cu-custom>
			<view class="cu-card radius-lg bg-white margin-lr margin-top padding-sm">
				<view class="flex align-center justify-between">
					<view class="text-df text-bold">当前状态</view>
					<view v-if="callingDevice" class="text-bold text-green">{{callingDevice}}</view>
				</view>
				<view class="flex justify-center align-center flex-direction border-round margin-lr-auto margin-bottom-sm">
					<view class="text-xl">{{ stateText}}</view>
					<view class="icon-size margin-tb-sm" :class="'icon-'+stateIcon"></view>
					<view class="text-lg">
						<!-- <timer ref="timer"></timer> -->
					</view>
				</view>
			</view>
			<view class="cu-card radius-lg bg-white margin-lr margin-top padding-sm">
				<view class="flex align-center justify-between">
					<view class="text-df text-bold">状态选择</view>
				</view>
				<view class="grid col-2 margin-top-xxl margin-bottom-xl">
					<view class="flex justify-center align-center">
						<view :class="(isDisable || errText || ['振铃中', '就绪', '通话中'].includes(stateText)) ? 'bg-gray' : 'bg-white '" class="shadow text-center radius-20 padding-tb-sm padding-lr-lg card-box"
						 @tap="handleState('Ready', 0, '就绪')">
							<view class="icon-size icon-ready"></view>
							<view class="text-15">就绪</view>
						</view>
					</view>
					<view class="flex justify-center align-center">
						<picker :disabled="errText || ['振铃中', '通话中'].includes(stateText)" @change="PickerChange" :value="pickIndex" :range="AgentStateList"
						 range-key="codeName">
							<view :class="(errText || ['振铃中', '通话中'].includes(stateText)) ? 'bg-gray' : 'bg-white '" class="shadow text-center radius-20 padding-tb-sm padding-lr-lg card-box">
								<view class="icon-size icon-rest"></view>
								<view class="text-15 cursor-p">
									<text>离席</text>
									<text class="cuIcon-right text-bold"></text>
								</view>
							</view>
						</picker>
					</view>
				</view>
				<!-- <view class="grid col-2 margin-tb-xxl">
					<view class="flex justify-center align-center">
						<view class="shadow bg-white text-center radius-20 padding-tb-sm padding-lr-lg card-box" @tap="toHistoricalCall">
							<view class="icon-size icon-list"></view>
							<view class="text-15 cursor-p">
								<text>历史记录</text>
								<text class="cuIcon-right text-bold"></text>
							</view>
						</view>
					</view>
				</view> -->
			</view>
			<view class="cu-modal bottom-modal" :class="modalName=='signOutModal'?'show':''">
				<view class="cu-dialog bg-transparent padding-lr-sm margin-bottom-xxl">
					<view class="bg-white margin-bottom-sm radius-13">
						<view class="padding-top padding-bottom-sm text-grey">您是否登出当前账号？</view>
						<hr class="solid">
						<view class="bg-white text-center radius-13 padding-tb" @tap="logOut">
							<text class="text-red dark text-xxl">确定</text>
						</view>
					</view>
					<view class="bg-white text-center radius-13 padding-tb" @tap="hideModal">
						<text class="text-blue dark text-xxl">取消</text>
					</view>
				</view>
			</view>
		</view>
		<view class="footer-box">
			<view class="text-center footer">
				<block v-if="errText">
					<text :class="['连接中...'].includes(errText) ? 'bg-grey' : 'bg-red'" class="line"></text>
					<text :class="['连接中...'].includes(errText) ? 'text-gray' : 'text-red'"  class="text bg-grey-l">
						{{errText}}
					</text>
				</block>
				<block v-else>
					<text class="line bg-green"></text>
					<text class="text-green text bg-grey-l">
						已连接
					</text>
				</block>
			</view>
		</view>
	</view>
</template>

<script>
	import {
		phoneSeparated
	} from '../../utils/index.js'
	import timer from '../../components/timer.vue'
	import {
		mapState,
		mapMutations
	} from 'vuex';
	export default {
		components: {
			timer
		},
		data() {
			return {
				isDisable: false,
				scoket: null,
				time: '00:00:00',
				modalName: null,
				pickIndex: 0,
				pickCurItem: {}
			}
		},
		computed: {
			stateIcon() {
				let text = ''
				switch (this.stateText) {
					case '未登录':
					case '未就绪':
						text = 'noReady';
						break;
					case '就绪':
						text = 'ready';
						break;
					case '话后处理':
						text = 'callAfter';
						break;
					case '振铃中':
						text = 'ringing ';
						break;
					case '通话中':
						text = 'calling';
						break;
					default:
						text = 'rest';
				}
				return text
			},
			...mapState([
				'AgentStateList',
				'message',
				'stateText',
				'callingDevice',
				'errText',
				'isLogin'
			])
		},
		watch: {},
		onLoad() {
			console.log('home on load')
		},
		created() {
			console.log('callingDevice ',this.callingDevice)
			console.log('home created')
			console.log('isLogin ',this.isLogin)
			// this.login()
		},
		mounted() {
			if(this.isLogin)return
			this.login()
			try {
				const state = uni.getStorageSync('state');
				if (state) {
					this.$refs.timer.startTimer()
				} else {
					uni.redirectTo({
						url: '/pages/login/login'
					})
				}
				uni.$on('updateStateText', (data) => {
					console.log('updateStateText')
					if (this.$refs.timer) {
						this.$refs.timer.stopTimer()
						this.$refs.timer.startTimer()
					}
				})
				const arr = this.$store.state.AgentStateList.map((item) => {
					return item.codename
				})
				this.$store.commit('setStateTextList', arr)
				this.$store.commit('setIsClick', false)
			} catch (e) {
				// error
				console.log(e)
			}
		},
		
		methods: {
			toHistoricalCall() {
				console.log('go to historical call page')
				uni.navigateTo({
					url: '/pages/historicalCall/historicalCall'
				})
			},
			login(){
				
				console.log('agent info ',this.globalData)
				
				let data = {
					stationId: this.globalData.userInfo.stationNum,
					agentId: this.globalData.userInfo.agentNum,
					acdId: "101",
					domain: this.globalData.userInfo.domain,
					agentState: "Login",
					reasoncode: 0,
					workMode: "AutoIn",
					remoteLogin: "remoteLogin",
					type: '1',
				}
				this.$Socket.send({
					data: JSON.stringify(data),
					success() {
						that.$store.commit('setIsClick', true)
						console.log('小休码更改成功')
					},
					fail() {
						console.log('小休码更改失败')
					}
				})
			},
			handleState(agentState, reasonCode, text) {
				if (this.errText) return
				if (text == '就绪') {
					if (this.isDisable || ['就绪', '通话中'].includes(this.stateText)) {
						return
					}
					this.isDisable = true
					setTimeout(() => {
						this.isDisable = false
					}, 1000)
				}
				let data = {
					stationId: this.globalData.userInfo.stationNum,
					agentId: this.globalData.userInfo.agentNum,
					acdId: "101",
					domain: this.globalData.userInfo.domain,
					reasonCode: reasonCode,
					agentState: agentState,
					workMode: "AutoIn",
					remoteLogin: "remoteLogin",
					type: "17"
				}
				const that = this
				this.$Socket.send({
					data: JSON.stringify(data),
					success() {
						that.$store.commit('setIsClick', true)
						console.log('小休码更改成功')
					},
					fail() {
						console.log('小休码更改失败')
					}
				})
			},
			PickerChange(e) {
				this.pickIndex = e.detail.value
				this.pickCurItem = this.AgentStateList[this.pickIndex]
				this.handleState('NotReady', this.pickCurItem.code, this.pickCurItem.codeName)
			},
			signOut(e) {
				if (this.errText || ['通话中'].includes(this.stateText)) return
				console.log('sign out')
				this.modalName = e.currentTarget.dataset.target
			},
			logOut(e) {
				this.modalName = null
				this.$refs.timer.stopTimer()
				const that = this
				const data = {
					reasonCode: '0',
					agentState: 'Logout',
					acdId: '101',
					workMode: 'Manual',
					type: "16",
					...that.message
				}
				this.$Socket.send({
					data: JSON.stringify(data),
					success() {}
				})

			},
			hideModal(e) {
				this.modalName = null
			}
		},
		onUnload() {
			console.log('home on unload')
		},
		onHide() {
			console.log('home on hide')
		},
		beforeDestroy() {
			console.log('before Destroy')
		}
	}
</script>

<style lang="scss" scoped>
	.border-round {
		border: 6px solid #00AEFF;
		border-radius: 200rpx;
		width: 320rpx;
		height: 320rpx;
	}

	.height-24 {
		height: 48rpx;
	}

	.icon-size {
		font-size: 120rpx;
		width: 120rpx;
		height: 120rpx;
		margin: 0 auto;
	}

	/* Solid border */
	hr.solid {
		border-top: 1px solid #D9D9D9;
	}

	.card-box {
		width: 160rpx;
		box-sizing: content-box;
	}
</style>
