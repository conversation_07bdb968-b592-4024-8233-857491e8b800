<template>
	<view class="dialer-container">
		<cu-custom bgColor="bg-gradual-blue" :isBack="false">
			<block slot="content">拨号盘</block>
		</cu-custom>

		<!-- 号码显示区域 -->
		<view class="number-display">
			<text class="phone-number">{{ phoneNumber || '' }}</text>
		</view>

		<!-- 拨号键盘 -->
		<view class="keypad">
			<view class="keypad-row" v-for="(row, rowIndex) in keypadLayout" :key="rowIndex">
				<view
					class="keypad-btn"
					v-for="(key, keyIndex) in row"
					:key="keyIndex"
					@click="inputNumber(key.number)"
					:class="{'special-key': key.number === '*' || key.number === '#'}"
				>
					<text class="key-number">{{ key.number }}</text>
					<text class="key-letters" v-if="key.letters">{{ key.letters }}</text>
				</view>
			</view>
		</view>

		<!-- 底部操作区域 -->
		<view class="bottom-actions">
			<view class="action-space"></view>
			<view class="call-btn" @click="makeCall">
				<image class="call-icon" src="/static/makecall.png"></image>
			</view>
			<view class="delete-btn" @click="deleteNumber" v-if="phoneNumber">
				<text class="delete-icon">×</text>
			</view>
			<view class="action-space" v-else></view>
		</view>
	</view>
</template>

<script>
export default {
	data() {
		return {
			phoneNumber: '',
			keypadLayout: [
				[
					{ number: '1', letters: '' },
					{ number: '2', letters: 'ABC' },
					{ number: '3', letters: 'DEF' }
				],
				[
					{ number: '4', letters: 'GHI' },
					{ number: '5', letters: 'JKL' },
					{ number: '6', letters: 'MNO' }
				],
				[
					{ number: '7', letters: 'PQRS' },
					{ number: '8', letters: 'TUV' },
					{ number: '9', letters: 'WXYZ' }
				],
				[
					{ number: '*', letters: '' },
					{ number: '0', letters: '+' },
					{ number: '#', letters: '' }
				]
			]
		}
	},
	methods: {
		// 输入数字
		inputNumber(key) {
			if (this.phoneNumber.length < 20) {
				this.phoneNumber += key;
			}
		},

		// 删除最后一位数字
		deleteNumber() {
			if (this.phoneNumber.length > 0) {
				this.phoneNumber = this.phoneNumber.slice(0, -1);
			}
		},

		// 拨打电话
		makeCall() {
			if (!this.phoneNumber) {
				uni.showToast({
					title: '请输入电话号码',
					icon: 'none'
				});
				return;
			}
			// 检查业务代码
			let businessCode = uni.getStorageSync('businessCode');
			if (!businessCode) {
				uni.showModal({
					title: '提示',
					content: '请先在设置页面选择业务，然后再进行外呼',
					showCancel: false,
					confirmText: '知道了'
				});
				return;
			}
			let req = {
				domain: detail.domain,
				businessCode,
				callerNum: this.globalData.userInfo.phone,
				calleeNum: this.phoneNumber,
				uui: 'customer_double_call_' + 666,
				authCode: "Get5pJnR"
			}
			
			let sign = this.createSign(req, "dN9L02Fx0x98o7dGRc7nMgNHA8uPB9iT");
			console.log("Generated Sign:", sign);
			req.sign = sign;
			
			this.httpPost("/call/yihu-outbound-unit/outboundUnit/mobileCall", req).then(res => {
				console.log('makecall resp:', res);
			
				if (res.data.code == 200) {
					uni.showToast({
						title: '拨打成功',
						icon: 'success',
						duration: 2000
					});
				} else {
					uni.showToast({
						title: res.data.message || '拨打失败',
						icon: 'none',
						duration: 2000
					});
				}
			}).catch(err => {
				console.log('拨号失败:', err);
				uni.showToast({
					title: '网络异常，请稍后重试',
					icon: 'none',
					duration: 2000
				});
			});
		},

		// 实际拨号功能
		dialPhone(number) {
			// 在真实环境中，这里会调用拨号API
			uni.makePhoneCall({
				phoneNumber: number,
				success: () => {
					console.log('拨号成功');
				},
				fail: (err) => {
					console.error('拨号失败:', err);
					uni.showToast({
						title: '拨号失败',
						icon: 'none'
					});
				}
			});
		}
	}
}
</script>

<style scoped>
.dialer-container {
	background: #f8f9fa;
	min-height: 100vh;
	padding-bottom: 120rpx;
}

/* 号码显示区域 */
.number-display {
	padding: 80rpx 40rpx 60rpx;
	text-align: center;
	min-height: 120rpx;
	display: flex;
	align-items: center;
	justify-content: center;
}

.phone-number {
	font-size: 48rpx;
	font-weight: 300;
	color: #333;
	letter-spacing: 4rpx;
	min-height: 60rpx;
	line-height: 60rpx;
}

/* 拨号键盘 */
.keypad {
	padding: 20rpx 60rpx;
	max-width: 600rpx;
	margin: 0 auto;
	margin-top: 150rpx;
}

.keypad-row {
	display: flex;
	justify-content: space-between;
	margin-bottom: 40rpx;
}

.keypad-btn {
	width: 140rpx;
	height: 140rpx;
	background: #e9ecef;
	border-radius: 50%;
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
	transition: all 0.15s ease;
	position: relative;
}

.keypad-btn:active {
	transform: scale(0.95);
	background: #dee2e6;
}

.key-number {
	font-size: 56rpx;
	font-weight: 300;
	color: #333;
	line-height: 1;
}

.key-letters {
	font-size: 20rpx;
	font-weight: 500;
	color: #666;
	margin-top: 4rpx;
	letter-spacing: 2rpx;
}

.special-key .key-number {
	font-size: 48rpx;
	color: #666;
}

/* 底部操作区域 */
.bottom-actions {
	position: fixed;
	bottom: 160rpx;
	left: 0;
	right: 0;
	display: flex;
	justify-content: space-between;
	align-items: center;
	padding: 0 80rpx;
}

.action-space {
	width: 120rpx;
	height: 120rpx;
}

.call-btn {
	width: 120rpx;
	height: 120rpx;
	background: #3BCF5F;
	border-radius: 50%;
	display: flex;
	align-items: center;
	justify-content: center;
	transition: all 0.15s ease;
	box-shadow: 0 8rpx 24rpx rgba(59, 207, 95, 0.3);
}

.call-btn:active {
	transform: scale(0.95);
}

.call-icon {
	width: 60rpx;
	height: 60rpx;
}

.delete-btn {
	width: 120rpx;
	height: 120rpx;
	background: #e9ecef;
	border-radius: 50%;
	display: flex;
	align-items: center;
	justify-content: center;
	transition: all 0.15s ease;
}

.delete-btn:active {
	transform: scale(0.95);
	background: #dee2e6;
}

.delete-icon {
	font-size: 48rpx;
	color: #666;
	font-weight: 300;
}
</style>
