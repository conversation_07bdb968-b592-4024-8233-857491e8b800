<template>
	<view class="dialer-container">
		<cu-custom bgColor="bg-gradual-blue" :isBack="false">
			<block slot="content">拨号盘</block>
		</cu-custom>
		
		<!-- 号码显示区域 -->
		<view class="number-display">
			<view class="number-input">
				<input 
					v-model="phoneNumber" 
					type="tel" 
					placeholder="请输入电话号码"
					class="number-field"
					maxlength="20"
				/>
			</view>
			<view class="clear-btn" @click="clearNumber" v-if="phoneNumber">
				<text class="icon-close"></text>
			</view>
		</view>
		
		<!-- 拨号键盘 -->
		<view class="keypad">
			<view class="keypad-row" v-for="(row, rowIndex) in keypadLayout" :key="rowIndex">
				<view 
					class="keypad-btn" 
					v-for="(key, keyIndex) in row" 
					:key="keyIndex"
					@click="inputNumber(key)"
					:class="{'special-key': key === '*' || key === '#'}"
				>
					<text class="key-text">{{ key }}</text>
				</view>
			</view>
		</view>
		
		<!-- 操作按钮区域 -->
		<view class="action-buttons">
			<view class="action-btn delete-btn" @click="deleteNumber">
				<text class="icon-back"></text>
			</view>
			<view class="action-btn call-btn" @click="makeCall" :class="{'disabled': !phoneNumber}">
				<text class="icon-phone"></text>
			</view>
			<view class="action-btn contact-btn" @click="openContacts">
				<text class="icon-group"></text>
			</view>
		</view>
	</view>
</template>

<script>
export default {
	data() {
		return {
			phoneNumber: '',
			keypadLayout: [
				['1', '2', '3'],
				['4', '5', '6'],
				['7', '8', '9'],
				['*', '0', '#']
			]
		}
	},
	methods: {
		// 输入数字
		inputNumber(key) {
			if (this.phoneNumber.length < 20) {
				this.phoneNumber += key;
			}
		},
		
		// 删除最后一位数字
		deleteNumber() {
			if (this.phoneNumber.length > 0) {
				this.phoneNumber = this.phoneNumber.slice(0, -1);
			}
		},
		
		// 清空号码
		clearNumber() {
			this.phoneNumber = '';
		},
		
		// 拨打电话
		makeCall() {
			if (!this.phoneNumber) {
				this.showText('请输入电话号码');
				return;
			}
			
			// 这里可以集成实际的拨号功能
			uni.showModal({
				title: '拨打电话',
				content: `确定要拨打 ${this.phoneNumber} 吗？`,
				success: (res) => {
					if (res.confirm) {
						// 实际的拨号逻辑
						this.dialPhone(this.phoneNumber);
					}
				}
			});
		},
		
		// 实际拨号功能
		dialPhone(number) {
			// 在真实环境中，这里会调用拨号API
			uni.makePhoneCall({
				phoneNumber: number,
				success: () => {
					console.log('拨号成功');
				},
				fail: (err) => {
					console.error('拨号失败:', err);
					this.showText('拨号失败');
				}
			});
		},
		
		// 打开联系人
		openContacts() {
			this.showText('联系人功能开发中');
		}
	}
}
</script>

<style scoped>
.dialer-container {
	background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
	min-height: 100vh;
	padding-bottom: 120rpx;
}

/* 号码显示区域 */
.number-display {
	padding: 60rpx 40rpx 40rpx;
	display: flex;
	align-items: center;
	justify-content: center;
	position: relative;
}

.number-input {
	flex: 1;
	max-width: 600rpx;
}

.number-field {
	width: 100%;
	height: 100rpx;
	background: rgba(255, 255, 255, 0.9);
	border-radius: 20rpx;
	padding: 0 30rpx;
	font-size: 36rpx;
	text-align: center;
	color: #333;
	border: none;
	box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.1);
}

.clear-btn {
	position: absolute;
	right: 60rpx;
	width: 60rpx;
	height: 60rpx;
	background: rgba(255, 255, 255, 0.2);
	border-radius: 50%;
	display: flex;
	align-items: center;
	justify-content: center;
	color: white;
	font-size: 32rpx;
}

/* 拨号键盘 */
.keypad {
	padding: 40rpx;
	max-width: 600rpx;
	margin: 0 auto;
}

.keypad-row {
	display: flex;
	justify-content: space-between;
	margin-bottom: 30rpx;
}

.keypad-btn {
	width: 120rpx;
	height: 120rpx;
	background: rgba(255, 255, 255, 0.9);
	border-radius: 50%;
	display: flex;
	align-items: center;
	justify-content: center;
	box-shadow: 0 8rpx 24rpx rgba(0, 0, 0, 0.15);
	transition: all 0.2s ease;
}

.keypad-btn:active {
	transform: scale(0.95);
	background: rgba(255, 255, 255, 0.8);
}

.key-text {
	font-size: 48rpx;
	font-weight: 500;
	color: #333;
}

.special-key .key-text {
	font-size: 40rpx;
	color: #666;
}

/* 操作按钮 */
.action-buttons {
	position: fixed;
	bottom: 160rpx;
	left: 0;
	right: 0;
	display: flex;
	justify-content: space-around;
	padding: 0 80rpx;
}

.action-btn {
	width: 100rpx;
	height: 100rpx;
	border-radius: 50%;
	display: flex;
	align-items: center;
	justify-content: center;
	font-size: 40rpx;
	transition: all 0.2s ease;
}

.delete-btn {
	background: rgba(255, 255, 255, 0.2);
	color: white;
}

.call-btn {
	background: #4CAF50;
	color: white;
	box-shadow: 0 8rpx 24rpx rgba(76, 175, 80, 0.3);
}

.call-btn.disabled {
	background: rgba(255, 255, 255, 0.3);
	color: rgba(255, 255, 255, 0.5);
}

.contact-btn {
	background: rgba(255, 255, 255, 0.2);
	color: white;
}

.action-btn:active {
	transform: scale(0.95);
}
</style>
