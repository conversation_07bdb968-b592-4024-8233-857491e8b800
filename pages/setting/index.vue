<template>
	<view>
		<cu-custom bgColor="bg-gradual-blue" :isBack="false">
			<block slot="content">设置</block>
		</cu-custom>
		<scroll-view scroll-y class="scrollPage">
			<view class="cu-card case">
				<view class="cu-item shadow">
					<view class="cu-list menu-avatar">
						<view class="cu-item">
							<view class="cu-avatar bg-white round lg" style="background-image:url(/static/agent.png);">
							</view>
							<view class="content flex-sub">
								<view class="text-grey">{{userInfo.agentName}}</view>
								<view class="text-gray text-sm flex justify-between">
									坐席号{{userInfo.agentNum}} 手机号{{userInfo.phone}}
								</view>
							</view>
						</view>
					</view>
				</view>
			</view>
			<view class="cu-list menu card-menu margin-bottom-xl shadow-lg radius">
				<view class="cu-item arrow" @tap="showPhoneModal">
					<view class="content">
						<!-- <text class="icon-phone text-blue"></text> -->
						<text class="text-grey">修改手机号</text>
					</view>
				</view>
				<view class="cu-item arrow" @tap="showModal">
					<view class="content">
						<text class="icon-charts text-green"></text>
						<text class="text-grey">业务选择</text>
					</view>
				</view>
				<view class="cu-item arrow">
					<button class="cu-btn content" open-type="feedback">
						<text class="icon-view text-grey"></text>
						<text class="text-grey">意见反馈</text>
					</button>
				</view>
				<view class="fixed flex flex-direction margin-top-lg">
					<button class="cu-btn bg-red margin-tb-sm lg" @click="logout">退出登录</button>
				</view>
			</view>
			<view class="cu-modal" :class="modalName=='chooseBusinessModal'?'show':''" @tap="hideModal">
				<view class="cu-dialog" @tap.stop="">
					<radio-group class="block" @change="RadioChange">
						<view class="cu-list menu text-left">
							<view class="cu-item" v-for="(item,index) in businessList" :key="index">
								<label class="flex justify-between align-center flex-sub">
									<view class="flex-sub">{{item.name}}</view>
									<radio class="round" :class="radio==index?'checked':''"
										:checked="radio==index?true:false" :value="index + ''"></radio>
								</label>
							</view>
						</view>
					</radio-group>
				</view>
			</view>

			<!-- 修改手机号弹窗 -->
			<view class="cu-modal" :class="modalName=='phoneModal'?'show':''" @tap="hidePhoneModal">
				<view class="cu-dialog" @tap.stop="">
					<view class="cu-bar bg-white justify-end">
						<view class="content">修改手机号</view>
						<view class="action" @tap="hidePhoneModal">
							<text class="icon-close text-red"></text>
						</view>
					</view>
					<view class="padding-xl">
						<view class="cu-form-group">
							<!-- <view class="title">新手机号</view> -->
							<input v-model="newPhoneNumber" type="tel" placeholder="请输入11位手机号" maxlength="11" />
						</view>
						<view class="margin-top-lg">
							<button class="cu-btn bg-blue lg block" @tap="updatePhone">确认修改</button>
						</view>
					</view>
				</view>
			</view>
			<view class="cu-tabbar-height"></view>
		</scroll-view>
	</view>
</template>

<script>
	import store from '../../utils/store.js'
	import {
		initWs, reConnect, closeConnect
	} from '../../utils/useSocket.js'
	export default {
		data() {
			return {
				modalName: null,
				radio: -1,
				userInfo: null,
				businessList: [],
				businessCode: null,
				newPhoneNumber: ''
			}
		},
		created() {
			this.userInfo = this.globalData.userInfo
			if (!this.userInfo) {
				console.log(this.userInfo)
				uni.reLaunch({
					url: '/pages/login/index'
				});
				return
			}

			this.getBusinessList()
			this.businessCode = uni.getStorageSync('businessCode');
		},
		methods: {
			getBusinessList() {
				let req = {
					"domain": this.userInfo.domain
				}
				this.httpPost("/server/feign-yihu-conf/businessManager/queryList", req).then(res => {
					this.businessList = res.data.data
					this.businessList.forEach((item, index) => {
						if (item.code == this.businessCode) {
							this.radio = index
						}
					})
				}).catch(err => {
					this.showText('服务器异常');
				})
			},
			hideModal(e) {
				this.modalName = null
			},
			showModal() {
				this.modalName = 'chooseBusinessModal'
			},

			// 显示修改手机号弹窗
			showPhoneModal() {
				this.newPhoneNumber = this.userInfo.phone || '';
				this.modalName = 'phoneModal';
			},

			// 隐藏修改手机号弹窗
			hidePhoneModal() {
				this.modalName = null;
				this.newPhoneNumber = '';
			},

			// 校验手机号格式
			validatePhoneNumber(number) {
				const phoneRegex = /^1[3-9]\d{9}$/;
				return phoneRegex.test(number);
			},

			// 更新手机号
			updatePhone() {
				if (!this.newPhoneNumber) {
					uni.showToast({
						title: '请输入手机号',
						icon: 'none'
					});
					return;
				}

				if (!this.validatePhoneNumber(this.newPhoneNumber)) {
					uni.showToast({
						title: '请输入正确的11位手机号',
						icon: 'none'
					});
					return;
				}

				const req = {
					phone: this.newPhoneNumber
				};

				uni.showLoading({
					title: '修改中...'
				});

				this.httpPost("/server/crm-system/system/sys/agent/update", req).then(res => {
					uni.hideLoading();
					console.log('更新手机号响应:', res);

					if (res.data.success) {
						// 更新本地用户信息
						this.userInfo.phone = this.newPhoneNumber;
						this.globalData.userInfo.phone = this.newPhoneNumber;

						// 更新本地存储
						uni.setStorage({
							key: 'userInfo',
							data: this.userInfo
						});

						uni.showToast({
							title: '修改成功',
							icon: 'success'
						});

						this.hidePhoneModal();
					} else {
						uni.showToast({
							title: res.data.message || '修改失败',
							icon: 'none'
						});
					}
				}).catch(err => {
					uni.hideLoading();
					console.error('更新手机号失败:', err);
					uni.showToast({
						title: '网络异常，请稍后重试',
						icon: 'none'
					});
				});
			},
			RadioChange(e) {
				this.radio = e.detail.value
				console.log('radio ', this.businessList[this.radio])
				uni.setStorage({
					key: 'businessCode',
					data: this.businessList[this.radio].code
				})
			},
			logout() {
				uni.showModal({
					title: '警告',
					content: '确定退出？',
					success: function (res) {
						if (res.confirm) {
							console.log('用户点击确定');
							uni.removeStorageSync('token');
							uni.removeStorageSync('userInfo');
							store.commit('setLoginState',false)
							closeConnect(true)
							uni.reLaunch({
								url: '/pages/login/index'
							});
						} else if (res.cancel) {
							console.log('用户点击取消');
						}
					}
				});
			},
			chooseBusiness() {

			}
		}
	}
</script>

<style scoped>
	/* 页面容器 */
	.setting-page {
		background: #f5f7fa;
		min-height: 100vh;
	}

	.page-content {
		height: 100vh;
		padding: 20rpx;
	}

	/* 个人信息区域 */
	.profile-section {
		margin-bottom: 32rpx;
	}

	.profile-card {
		position: relative;
		background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
		border-radius: 32rpx;
		padding: 48rpx 32rpx;
		overflow: hidden;
		box-shadow: 0 8rpx 32rpx rgba(102, 126, 234, 0.3);
	}

	/* 背景装饰 */
	.card-background {
		position: absolute;
		top: 0;
		left: 0;
		right: 0;
		bottom: 0;
		overflow: hidden;
	}

	.bg-circle {
		position: absolute;
		border-radius: 50%;
		background: rgba(255, 255, 255, 0.1);
	}

	.circle-1 {
		width: 200rpx;
		height: 200rpx;
		top: -100rpx;
		right: -50rpx;
	}

	.circle-2 {
		width: 120rpx;
		height: 120rpx;
		bottom: -60rpx;
		left: -30rpx;
	}

	/* 用户头像区域 */
	.user-header {
		position: relative;
		z-index: 2;
		display: flex;
		align-items: center;
		margin-bottom: 40rpx;
	}

	.avatar-container {
		position: relative;
		margin-right: 24rpx;
	}

	.user-avatar {
		width: 120rpx;
		height: 120rpx;
		border-radius: 60rpx;
		border: 4rpx solid rgba(255, 255, 255, 0.3);
	}

	.status-dot {
		position: absolute;
		bottom: 8rpx;
		right: 8rpx;
		width: 24rpx;
		height: 24rpx;
		background: #52c41a;
		border-radius: 50%;
		border: 3rpx solid #ffffff;
	}

	.user-basic-info {
		flex: 1;
	}

	.user-name {
		font-size: 40rpx;
		font-weight: 700;
		color: #ffffff;
		margin-bottom: 8rpx;
		text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.1);
	}

	.user-title {
		font-size: 26rpx;
		color: rgba(255, 255, 255, 0.8);
		background: rgba(255, 255, 255, 0.2);
		padding: 8rpx 16rpx;
		border-radius: 20rpx;
		display: inline-block;
	}

	/* 信息网格 */
	.info-grid {
		position: relative;
		z-index: 2;
		display: grid;
		grid-template-columns: 1fr 1fr;
		gap: 24rpx;
	}

	.info-item {
		background: rgba(255, 255, 255, 0.15);
		border-radius: 20rpx;
		padding: 24rpx;
		backdrop-filter: blur(10rpx);
		border: 1rpx solid rgba(255, 255, 255, 0.2);
	}

	.info-icon-box {
		width: 48rpx;
		height: 48rpx;
		border-radius: 24rpx;
		display: flex;
		align-items: center;
		justify-content: center;
		margin-bottom: 16rpx;
	}

	.info-icon-box.agent {
		background: rgba(255, 255, 255, 0.2);
	}

	.info-icon-box.phone {
		background: rgba(255, 255, 255, 0.2);
	}

	.info-icon-box.domain {
		background: rgba(255, 255, 255, 0.2);
	}

	.info-icon-box.business {
		background: rgba(255, 255, 255, 0.2);
	}

	.info-icon {
		font-size: 24rpx;
	}

	.info-text {
		color: #ffffff;
	}

	.info-label {
		display: block;
		font-size: 22rpx;
		color: rgba(255, 255, 255, 0.7);
		margin-bottom: 4rpx;
	}

	.info-value {
		font-size: 26rpx;
		font-weight: 600;
		color: #ffffff;
	}

	/* 菜单区域 */
	.menu-section {
		margin-bottom: 32rpx;
	}

	.menu-card {
		background: #ffffff;
		border-radius: 24rpx;
		overflow: hidden;
		box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.06);
	}

	.menu-item {
		display: flex;
		align-items: center;
		padding: 32rpx;
		transition: all 0.3s ease;
		position: relative;
	}

	.menu-item::after {
		content: '';
		position: absolute;
		left: 0;
		right: 0;
		bottom: 0;
		height: 2rpx;
		background: linear-gradient(90deg, transparent, #f0f2f5, transparent);
	}

	.menu-item:last-child::after {
		display: none;
	}

	.menu-item:active {
		background: linear-gradient(135deg, #f8f9ff 0%, #f0f4ff 100%);
		transform: scale(0.98);
	}

	.menu-button {
		width: 100%;
		display: flex;
		align-items: center;
		background: transparent;
		border: none;
		padding: 0;
		text-align: left;
	}

	.menu-icon-wrapper {
		width: 72rpx;
		height: 72rpx;
		border-radius: 20rpx;
		display: flex;
		align-items: center;
		justify-content: center;
		margin-right: 24rpx;
		position: relative;
		overflow: hidden;
	}

	.menu-icon-wrapper::before {
		content: '';
		position: absolute;
		top: 0;
		left: 0;
		right: 0;
		bottom: 0;
		background: inherit;
		opacity: 0.1;
	}

	.menu-icon-wrapper.business {
		background: linear-gradient(135deg, #52c41a 0%, #73d13d 100%);
	}

	.menu-icon-wrapper.feedback {
		background: linear-gradient(135deg, #faad14 0%, #ffc53d 100%);
	}

	.menu-icon {
		font-size: 32rpx;
		color: #ffffff;
		position: relative;
		z-index: 1;
	}

	.menu-content {
		flex: 1;
	}

	.menu-title {
		display: block;
		font-size: 32rpx;
		color: #333333;
		font-weight: 600;
		margin-bottom: 6rpx;
	}

	.menu-subtitle {
		font-size: 24rpx;
		color: #999999;
		line-height: 1.4;
	}

	.menu-arrow {
		font-size: 28rpx;
		color: #cccccc;
		transition: transform 0.3s ease;
	}

	.menu-item:active .menu-arrow {
		transform: translateX(4rpx);
	}

	.menu-divider {
		height: 1rpx;
		background: linear-gradient(90deg, transparent, #f0f2f5, transparent);
		margin: 0 32rpx;
	}

	/* 退出登录区域 */
	.logout-section {
		margin-bottom: 40rpx;
	}

	.logout-btn {
		width: 100%;
		background: linear-gradient(135deg, #ff4757 0%, #ff6b7a 100%);
		color: #ffffff;
		border: none;
		border-radius: 24rpx;
		padding: 32rpx;
		display: flex;
		align-items: center;
		justify-content: center;
		gap: 16rpx;
		box-shadow: 0 8rpx 24rpx rgba(255, 71, 87, 0.3);
		transition: all 0.3s ease;
	}

	.logout-btn:active {
		transform: translateY(2rpx);
		box-shadow: 0 4rpx 16rpx rgba(255, 71, 87, 0.4);
	}

	.logout-icon {
		font-size: 32rpx;
	}

	.logout-text {
		font-size: 32rpx;
		font-weight: 600;
	}

	/* 业务选择弹窗 */
	.business-modal {
		position: fixed;
		top: 0;
		left: 0;
		right: 0;
		bottom: 0;
		background: rgba(0, 0, 0, 0.5);
		display: flex;
		align-items: center;
		justify-content: center;
		z-index: 9999;
		opacity: 0;
		visibility: hidden;
		transition: all 0.3s ease;
	}

	.business-modal.show {
		opacity: 1;
		visibility: visible;
	}

	.modal-content {
		background: #ffffff;
		border-radius: 24rpx;
		width: 90%;
		max-width: 600rpx;
		max-height: 80vh;
		overflow: hidden;
		transform: scale(0.8);
		transition: transform 0.3s ease;
	}

	.business-modal.show .modal-content {
		transform: scale(1);
	}

	.modal-header {
		display: flex;
		align-items: center;
		justify-content: space-between;
		padding: 40rpx;
		border-bottom: 2rpx solid #f0f2f5;
	}

	.modal-title {
		font-size: 32rpx;
		font-weight: 600;
		color: #333333;
	}

	.modal-close {
		font-size: 32rpx;
		color: #999999;
		padding: 8rpx;
	}

	.modal-body {
		max-height: 60vh;
		overflow-y: auto;
	}

	.business-list {
		padding: 20rpx 0;
	}

	.business-item {
		transition: background-color 0.3s ease;
	}

	.business-item.selected {
		background: #f6ffed;
	}

	.business-label {
		display: flex;
		align-items: center;
		justify-content: space-between;
		padding: 24rpx 40rpx;
		width: 100%;
	}

	.business-info {
		flex: 1;
	}

	.business-name {
		display: block;
		font-size: 30rpx;
		color: #333333;
		font-weight: 500;
		margin-bottom: 8rpx;
	}

	.business-code {
		font-size: 24rpx;
		color: #999999;
	}

	.business-radio {
		transform: scale(1.2);
	}

	.modal-footer {
		display: flex;
		border-top: 2rpx solid #f0f2f5;
	}

	.modal-btn {
		flex: 1;
		padding: 32rpx;
		border: none;
		font-size: 30rpx;
		font-weight: 500;
	}

	.modal-btn.cancel {
		background: #ffffff;
		color: #999999;
	}

	.modal-btn.confirm {
		background: linear-gradient(135deg, #52c41a 0%, #73d13d 100%);
		color: #ffffff;
	}

	/* 安全区域 */
	.safe-area-bottom {
		height: env(safe-area-inset-bottom);
		background: #f5f7fa;
	}

	/* 响应式适配 */
	@media (max-width: 750rpx) {
		.page-content {
			padding: 16rpx;
		}

		.profile-card {
			padding: 32rpx;
		}

		.avatar-image {
			width: 100rpx;
			height: 100rpx;
			border-radius: 50rpx;
		}

		.user-name {
			font-size: 32rpx;
		}

		.menu-item {
			padding: 28rpx;
		}

		.logout-btn {
			padding: 28rpx;
		}
	}

	@media (max-width: 600rpx) {
		.profile-header {
			margin-bottom: 32rpx;
		}

		.avatar-image {
			width: 80rpx;
			height: 80rpx;
			border-radius: 40rpx;
		}

		.user-name {
			font-size: 28rpx;
		}

		.detail-item {
			padding: 16rpx 0;
		}

		.menu-item {
			padding: 24rpx;
		}

		.modal-header {
			padding: 32rpx;
		}

		.business-label {
			padding: 20rpx 32rpx;
		}
	}
</style>

	/* 退出登录区域 */
	.logout-section {
		margin-bottom: 40rpx;
	}

	.logout-btn {
		width: 100%;
		background: linear-gradient(135deg, #ff4757 0%, #ff6b7a 100%);
		color: #ffffff;
		border: none;
		border-radius: 24rpx;
		padding: 32rpx;
		display: flex;
		align-items: center;
		justify-content: center;
		gap: 16rpx;
		box-shadow: 0 8rpx 24rpx rgba(255, 71, 87, 0.3);
		transition: all 0.3s ease;
	}

	.logout-btn:active {
		transform: translateY(2rpx);
		box-shadow: 0 4rpx 16rpx rgba(255, 71, 87, 0.4);
	}

	.logout-icon {
		font-size: 32rpx;
	}

	.logout-text {
		font-size: 32rpx;
		font-weight: 600;
	}

	/* 业务选择弹窗 */
	.business-modal {
		position: fixed;
		top: 0;
		left: 0;
		right: 0;
		bottom: 0;
		background: rgba(0, 0, 0, 0.5);
		display: flex;
		align-items: center;
		justify-content: center;
		z-index: 9999;
		opacity: 0;
		visibility: hidden;
		transition: all 0.3s ease;
	}

	.business-modal.show {
		opacity: 1;
		visibility: visible;
	}

	.modal-content {
		background: #ffffff;
		border-radius: 24rpx;
		width: 90%;
		max-width: 600rpx;
		max-height: 80vh;
		overflow: hidden;
		transform: scale(0.8);
		transition: transform 0.3s ease;
	}

	.business-modal.show .modal-content {
		transform: scale(1);
	}

	.modal-header {
		display: flex;
		align-items: center;
		justify-content: space-between;
		padding: 40rpx;
		border-bottom: 2rpx solid #f0f2f5;
	}

	.modal-title {
		font-size: 32rpx;
		font-weight: 600;
		color: #333333;
	}

	.modal-close {
		font-size: 32rpx;
		color: #999999;
		padding: 8rpx;
	}

	.modal-body {
		max-height: 60vh;
		overflow-y: auto;
	}

	.business-list {
		padding: 20rpx 0;
	}

	.business-item {
		transition: background-color 0.3s ease;
	}

	.business-item.selected {
		background: #f6ffed;
	}

	.business-label {
		display: flex;
		align-items: center;
		justify-content: space-between;
		padding: 24rpx 40rpx;
		width: 100%;
	}

	.business-info {
		flex: 1;
	}

	.business-name {
		display: block;
		font-size: 30rpx;
		color: #333333;
		font-weight: 500;
		margin-bottom: 8rpx;
	}

	.business-code {
		font-size: 24rpx;
		color: #999999;
	}

	.business-radio {
		transform: scale(1.2);
	}

	.modal-footer {
		display: flex;
		border-top: 2rpx solid #f0f2f5;
	}

	.modal-btn {
		flex: 1;
		padding: 32rpx;
		border: none;
		font-size: 30rpx;
		font-weight: 500;
	}

	.modal-btn.cancel {
		background: #ffffff;
		color: #999999;
	}

	.modal-btn.confirm {
		background: linear-gradient(135deg, #52c41a 0%, #73d13d 100%);
		color: #ffffff;
	}

	/* 安全区域 */
	.safe-area-bottom {
		height: env(safe-area-inset-bottom);
		background: #f5f7fa;
	}

	/* 响应式适配 */
	@media (max-width: 750rpx) {
		.page-content {
			padding: 16rpx;
		}

		.profile-card {
			padding: 32rpx;
		}

		.avatar-image {
			width: 100rpx;
			height: 100rpx;
			border-radius: 50rpx;
		}

		.user-name {
			font-size: 32rpx;
		}

		.menu-item {
			padding: 28rpx;
		}

		.logout-btn {
			padding: 28rpx;
		}
	}

	@media (max-width: 600rpx) {
		.profile-header {
			margin-bottom: 32rpx;
		}

		.avatar-image {
			width: 80rpx;
			height: 80rpx;
			border-radius: 40rpx;
		}

		.user-name {
			font-size: 28rpx;
		}

		.detail-item {
			padding: 16rpx 0;
		}

		.menu-item {
			padding: 24rpx;
		}

		.modal-header {
			padding: 32rpx;
		}

		.business-label {
			padding: 20rpx 32rpx;
		}
	}
</style>