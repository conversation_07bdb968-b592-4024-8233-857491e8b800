<template>
	<view class="task-page">
		<cu-custom bgColor="bg-gradual-blue" :isBack="false">
			<block slot="content">任务列表</block>
		</cu-custom>

		<!-- 现代化标签栏 -->
		<view class="modern-tabs-container">
			<scroll-view scroll-x class="tabs-scroll">
				<view class="tabs-wrapper">
					<view
						class="tab-item"
						:class="{'active': index === TabCur}"
						v-for="(item,index) in taskTabbar"
						:key="index"
						@tap="tabSelect"
						:data-id="index"
					>
						<text class="tab-text">{{item.label}}</text>
						<view class="tab-indicator" v-if="index === TabCur"></view>
					</view>
				</view>
			</scroll-view>
		</view>

		<!-- 任务列表 -->
		<scroll-view scroll-y class="task-list-container">
			<view class="task-list">
				<view
					class="task-card"
					v-for="(item,index) in taskList"
					:key="index"
					:data-id="index"
					@tap="toTaskDetail"
				>
					<view class="card-header">
						<view class="task-title">{{item.taskName}}</view>
						<view class="task-status" :class="getStatusClass(item.status)">
							<view class="status-dot"></view>
							<text class="status-text">{{getStatusText(item.status)}}</text>
						</view>
					</view>

					<view class="card-content">
						<view class="task-description">
							<text class="description-text">{{item.taskDescription || '暂无描述'}}</text>
						</view>

						<view class="task-meta">
							<view class="meta-item">
								<text class="meta-icon">📅</text>
								<text class="meta-text">{{formatTime(item.createTime)}}</text>
							</view>
						</view>
					</view>

					<view class="card-footer">
						<view class="task-progress">
							<view class="progress-label">进度</view>
							<view class="progress-bar">
								<view class="progress-fill" :style="{width: getProgress(item) + '%'}"></view>
							</view>
							<view class="progress-text">{{getProgress(item)}}%</view>
						</view>
					</view>
				</view>

				<!-- 空状态 -->
				<view class="empty-state" v-if="taskList.length === 0">
					<view class="empty-icon">📋</view>
					<view class="empty-title">暂无任务</view>
					<view class="empty-subtitle">当前状态下没有任务数据</view>
				</view>

				<!-- 底部安全区域 -->
				<view class="bottom-safe-area"></view>
			</view>
		</scroll-view>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				TabCur:0,
				taskTabbar: [
					{label:"进行中",status:"1"},
					{label:"暂停",status:"2"},
					{label:"完成",status:"3"}
				],
				taskList:[]
			}
		},
		created() {
			console.log('task show')
			this.getTaskList("1")

			// 监听任务详情页面返回时的刷新事件
			uni.$on('refreshTaskList', () => {
				console.log('收到刷新任务列表事件')

				// 显示刷新提示
				uni.showToast({
					title: '刷新中...',
					icon: 'loading',
					duration: 1000,
					mask: true
				})

				// 刷新当前选中状态的任务列表
				const currentStatus = this.taskTabbar[this.TabCur].status
				this.getTaskList(currentStatus)
			})
		},
		onShow() {
			// 页面显示时检查是否需要刷新
			// 这里可以根据需要添加额外的刷新逻辑
			console.log('任务列表页面显示')
		},
		methods: {
			toTaskDetail(val){
				let index = val.currentTarget.dataset.id
				const taskInfo = this.taskList[index]
				uni.navigateTo({
					url: '/pages/taskdetail/index?id=' + taskInfo.id
				});
			},
			getTaskList(status){
				let req = {status, taskName: null}
				this.httpPost("/server/crm-preview-outbound/preview-outbound/previewTask/list?startPage=1&pageNum=100",req).then(res => {
					console.log(res.data.data.pageData)
					this.taskList = res.data.data.pageData

					// 隐藏加载提示
					uni.hideToast()
				}).catch(err => {
					// 隐藏加载提示
					uni.hideToast()

					uni.showToast({
						title: '获取任务列表失败',
						icon: 'none',
						duration: 2000
					})
					console.log('获取任务列表失败:', err)
				})
			},
			tabSelect(val){
				console.log(val)

				let index = val.currentTarget.dataset.id
				this.TabCur = index
				let status = this.taskTabbar[index].status
				this.getTaskList(status)
			},
			// 获取状态样式类
			getStatusClass(status) {
				const statusMap = {
					'1': 'status-running',
					'2': 'status-paused',
					'3': 'status-completed'
				}
				return statusMap[status] || 'status-default'
			},
			// 获取状态文本
			getStatusText(status) {
				const statusMap = {
					'1': '进行中',
					'2': '已暂停',
					'3': '已完成'
				}
				return statusMap[status] || '未知'
			},
			// 格式化时间
			formatTime(timeStr) {
				if (!timeStr) return '暂无时间'
				const date = new Date(timeStr)
				const now = new Date()
				const diff = now - date
				const days = Math.floor(diff / (1000 * 60 * 60 * 24))

				if (days === 0) {
					return '今天'
				} else if (days === 1) {
					return '昨天'
				} else if (days < 7) {
					return `${days}天前`
				} else {
					return timeStr.split(' ')[0]
				}
			},
			// 获取任务进度（只计算当前坐席的进度）
			getProgress(item) {
				if (!item.previewCustomer || !Array.isArray(item.previewCustomer)) {
					return 0;
				}

				const currentAgentNum = this.globalData.userInfo.agentNum;

				// 筛选出当前坐席的客户
				const myCustomers = item.previewCustomer.filter(customer =>
					customer.agentNum === currentAgentNum
				);

				if (myCustomers.length === 0) {
					return 0;
				}

				// 计算已拨打的客户数量（status为1或2都算已拨打）
				const calledCount = myCustomers.filter(customer =>
					customer.status === 1 || customer.status === 2
				).length;

				// 计算进度百分比
				const progress = Math.round((calledCount / myCustomers.length) * 100);
				return progress;
			}
		},
		beforeDestroy() {
			// 页面销毁时移除事件监听，避免内存泄漏
			uni.$off('refreshTaskList');
		}
	}
</script>

<style scoped>
/* 任务页面容器 */
.task-page {
	background-color: var(--bg-secondary, #FAFAFA);
	min-height: 100vh;
}

/* 现代化标签栏 */
.modern-tabs-container {
	background: #FFFFFF;
	box-shadow: 0 2rpx 16rpx rgba(0, 0, 0, 0.06);
	position: sticky;
	top: 0;
	z-index: 10;
}

.tabs-scroll {
	white-space: nowrap;
}

.tabs-wrapper {
	display: flex;
	padding: 0 24rpx;
}

.tab-item {
	position: relative;
	padding: 32rpx 24rpx;
	margin-right: 32rpx;
	cursor: pointer;
	transition: all 0.3s ease;
	white-space: nowrap;
}

.tab-text {
	font-size: 28rpx;
	font-weight: 500;
	color: var(--text-tertiary, #8C8C8C);
	transition: all 0.3s ease;
}

.tab-item.active .tab-text {
	color: var(--primary-color, #1890FF);
	font-weight: 600;
}

.tab-indicator {
	position: absolute;
	bottom: 0;
	left: 50%;
	transform: translateX(-50%);
	width: 40rpx;
	height: 6rpx;
	background: linear-gradient(135deg, var(--primary-color, #1890FF) 0%, var(--primary-light, #40A9FF) 100%);
	border-radius: 3rpx;
	animation: slideIn 0.3s ease;
}

/* 任务列表容器 */
.task-list-container {
	flex: 1;
	padding: 24rpx;
	height: calc(100vh - 200rpx); /* 减去导航栏和标签栏高度 */
}

.task-list {
	display: flex;
	flex-direction: column;
	gap: 24rpx;
}

/* 底部安全区域 */
.bottom-safe-area {
	height: calc(120rpx + env(safe-area-inset-bottom)); /* tabbar高度 + 底部安全区域 */
	background: transparent;
}

/* 任务卡片 */
.task-card {
	background: #FFFFFF;
	border-radius: 16rpx;
	padding: 32rpx;
	box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.06);
	transition: all 0.3s ease;
	position: relative;
	overflow: hidden;
}

.task-card::before {
	content: '';
	position: absolute;
	top: 0;
	left: 0;
	width: 8rpx;
	height: 100%;
	background: linear-gradient(135deg, var(--primary-color, #1890FF) 0%, var(--primary-light, #40A9FF) 100%);
}

.task-card:active {
	transform: translateY(-4rpx);
	box-shadow: 0 8rpx 24rpx rgba(0, 0, 0, 0.12);
}

/* 卡片头部 */
.card-header {
	display: flex;
	justify-content: space-between;
	align-items: flex-start;
	margin-bottom: 24rpx;
}

.task-title {
	font-size: 32rpx;
	font-weight: 600;
	color: var(--text-primary, #262626);
	line-height: 1.4;
	flex: 1;
	margin-right: 16rpx;
}

.task-status {
	display: flex;
	align-items: center;
	padding: 8rpx 16rpx;
	border-radius: 20rpx;
	font-size: 22rpx;
	font-weight: 500;
	white-space: nowrap;
}

.status-dot {
	width: 12rpx;
	height: 12rpx;
	border-radius: 50%;
	margin-right: 8rpx;
}

.status-running {
	background: var(--success-bg, #F6FFED);
	color: var(--success-dark, #389E0D);
}

.status-running .status-dot {
	background: var(--success-color, #52C41A);
}

.status-paused {
	background: var(--warning-bg, #FFF7E6);
	color: var(--warning-dark, #D46B08);
}

.status-paused .status-dot {
	background: var(--warning-color, #FA8C16);
}

.status-completed {
	background: var(--info-bg, #E6F7FF);
	color: var(--info-dark, #096DD9);
}

.status-completed .status-dot {
	background: var(--info-color, #1890FF);
}

/* 卡片内容 */
.card-content {
	margin-bottom: 24rpx;
}

.task-description {
	margin-bottom: 16rpx;
}

.description-text {
	font-size: 26rpx;
	color: var(--text-secondary, #595959);
	line-height: 1.5;
	display: -webkit-box;
	-webkit-box-orient: vertical;
	-webkit-line-clamp: 2;
	overflow: hidden;
}

.task-meta {
	display: flex;
	flex-wrap: wrap;
	gap: 16rpx;
}

.meta-item {
	display: flex;
	align-items: center;
	font-size: 24rpx;
	color: var(--text-tertiary, #8C8C8C);
}

.meta-icon {
	margin-right: 8rpx;
	font-size: 20rpx;
}

/* 卡片底部 */
.card-footer {
	border-top: 1rpx solid var(--border-color-light, #F0F0F0);
	padding-top: 24rpx;
}

.task-progress {
	display: flex;
	align-items: center;
	gap: 16rpx;
}

.progress-label {
	font-size: 24rpx;
	color: var(--text-tertiary, #8C8C8C);
	white-space: nowrap;
}

.progress-bar {
	flex: 1;
	height: 8rpx;
	background: var(--bg-tertiary, #F5F5F5);
	border-radius: 4rpx;
	overflow: hidden;
}

.progress-fill {
	height: 100%;
	background: linear-gradient(135deg, var(--primary-color, #1890FF) 0%, var(--primary-light, #40A9FF) 100%);
	border-radius: 4rpx;
	transition: width 0.3s ease;
}

.progress-text {
	font-size: 24rpx;
	color: var(--text-tertiary, #8C8C8C);
	white-space: nowrap;
	min-width: 60rpx;
	text-align: right;
}

/* 空状态 */
.empty-state {
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
	padding: 120rpx 48rpx;
	text-align: center;
}

.empty-icon {
	font-size: 120rpx;
	margin-bottom: 32rpx;
	opacity: 0.6;
}

.empty-title {
	font-size: 32rpx;
	font-weight: 600;
	color: var(--text-secondary, #595959);
	margin-bottom: 16rpx;
}

.empty-subtitle {
	font-size: 26rpx;
	color: var(--text-tertiary, #8C8C8C);
	line-height: 1.4;
}

/* 动画 */
@keyframes slideIn {
	from {
		opacity: 0;
		transform: translateX(-50%) translateY(10rpx);
	}
	to {
		opacity: 1;
		transform: translateX(-50%) translateY(0);
	}
}

/* 响应式适配 */
@media (max-width: 750rpx) {
	.task-list-container {
		padding: 16rpx;
	}

	.task-card {
		padding: 24rpx;
	}

	.task-title {
		font-size: 28rpx;
	}

	.tabs-wrapper {
		padding: 0 16rpx;
	}

	.tab-item {
		padding: 24rpx 16rpx;
		margin-right: 24rpx;
	}
}

/* 深色模式支持 */
@media (prefers-color-scheme: dark) {
	.task-page {
		background-color: #1a1a1a;
	}

	.modern-tabs-container {
		background: #262626;
	}

	.task-card {
		background: #262626;
	}

	.task-title {
		color: #FFFFFF;
	}

	.description-text {
		color: rgba(255, 255, 255, 0.8);
	}

	.card-footer {
		border-top-color: rgba(255, 255, 255, 0.1);
	}
}
</style>