	<template>
		<view class="customer-page">
			<!-- 自定义导航栏 -->
			<cu-custom bgColor="bg-gradual-blue" :isBack="false">
				<block slot="content">客户管理</block>
			</cu-custom>

			<!-- 搜索栏 -->
			<view class="search-section" :style="[{top: CustomBar + 'px'}]">
				<view class="search-wrapper">
					<view class="search-input-wrapper">
						<text class="search-icon cuIcon-search"></text>
						<input
							class="search-input"
							type="text"
							placeholder="输入客户姓名或电话号码"
							v-model="customerName"
							confirm-type="search"
							@input="onSearchInput"
						/>
						<text class="clear-icon cuIcon-close" v-if="customerName" @tap="clearSearch"></text>
					</view>
					<button class="search-btn" @tap="searchCustomer">
						<text class="search-btn-text">搜索</text>
					</button>
				</view>
			</view>

			<!-- 客户统计信息 -->
			<view class="stats-section" v-if="customerList.length > 0">
				<view class="stats-card">
					<view class="stats-item-single">
						<text class="stats-number">{{customerList.length}}</text>
						<text class="stats-label">客户总数</text>
					</view>
				</view>
			</view>

			<!-- 客户列表 -->
			<scroll-view
				scroll-y
				class="customer-list"
				:style="[{height: 'calc(100vh - ' + CustomBar + 'px - 200px)'}]"
				:scroll-with-animation="true"
				:enable-back-to-top="true"
			>
				<view class="list-container">
					<view
						class="customer-card"
						v-for="(item, index) in customerList"
						:key="index"
						:data-id="index"
					>
						<view class="customer-info">
							<view class="customer-header">
								<view class="customer-name">
									<text class="name-label">姓名：</text>
									<text class="name-value">{{item.name || '未知客户'}}</text>
								</view>
							</view>

							<view class="customer-phone">
								<text class="phone-label">联系方式：</text>
								<text class="phone-value">{{item.telephone || '暂无电话'}}</text>
							</view>

							<view class="customer-time" v-if="item.createTime">
								<text class="time-label">创建时间：</text>
								<text class="time-value">{{formatTime(item.createTime)}}</text>
							</view>

							<view class="customer-address" v-if="item.address">
								<text class="address-label">地址：</text>
								<text class="address-value">{{item.address}}</text>
							</view>
						</view>

						<view class="action-section">
							<button
								class="call-btn"
								:class="item.calling ? 'calling' : ''"
								:disabled="item.calling || !item.telephone"
								@tap="makecall(index)"
							>
								<text class="call-icon cuIcon-phone" v-if="!item.calling"></text>
								<text class="loading-icon cuIcon-loading2 cuIconfont-spin" v-if="item.calling"></text>
								<text class="call-text">{{item.calling ? '拨打中' : '拨号'}}</text>
							</button>
						</view>
					</view>

					<!-- 空状态 -->
					<view class="empty-state" v-if="customerList.length === 0 && !loading">
						<text class="empty-icon cuIcon-infofill"></text>
						<text class="empty-text">{{customerName ? '未找到相关客户' : '暂无客户数据'}}</text>
						<button class="refresh-btn" @tap="getCustomer">
							<text class="refresh-text">刷新数据</text>
						</button>
					</view>

					<!-- 加载状态 -->
					<view class="loading-state" v-if="loading">
						<text class="loading-icon cuIcon-loading2 cuIconfont-spin"></text>
						<text class="loading-text">加载中...</text>
					</view>

					<!-- 底部安全区域 -->
					<view class="bottom-safe-area"></view>
				</view>
			</scroll-view>
		</view>
	</template>

	<script>
		export default {
			data() {
				return {
					CustomBar: this.CustomBar,
					customerList: [],
					customerName: '',
					loading: false
				};
			},
			created() {
				console.log('customer page show')
				console.log('CustomBar ', this.CustomBar)
				this.getCustomer()
			},
			onReady() {
				let that = this;
				uni.createSelectorQuery().select('.customer-list').boundingClientRect(function(res) {
					that.barTop = res.top
				}).exec()
			},
			methods: {
				// 搜索客户
				searchCustomer() {
					if (!this.customerName.trim()) {
						uni.showToast({
							title: '请输入搜索关键词',
							icon: 'none',
							duration: 1500
						});
						return;
					}

					this.loading = true;
					console.log('搜索客户:', this.customerName);

					this.httpGet("/server/crm-customer/customer/list?page=1&pageNum=100&name=" + this.customerName).then(res => {
						console.log('customer search result:', res.data.data.records);
						if (res.data && res.data.data && res.data.data.records) {
							// 初始化客户状态
							this.customerList = res.data.data.records.map(item => ({
								...item,
								calling: false
							}));

							uni.showToast({
								title: `找到 ${this.customerList.length} 位客户`,
								icon: 'none',
								duration: 1500
							});
						} else {
							this.customerList = [];
						}
						this.loading = false;
					}).catch(err => {
						console.log('搜索客户失败:', err);
						this.loading = false;
						uni.showToast({
							title: '搜索失败，请重试',
							icon: 'none',
							duration: 2000
						});
					});
				},

				// 拨号功能
				makecall(val) {
					const detail = this.customerList[val];

					if (!detail.telephone) {
						uni.showToast({
							title: '该客户暂无电话号码',
							icon: 'none',
							duration: 2000
						});
						return;
					}

					// 检查业务代码
					let businessCode = uni.getStorageSync('businessCode');
					if (!businessCode) {
						uni.showModal({
							title: '提示',
							content: '请先在设置页面选择业务，然后再进行外呼',
							showCancel: false,
							confirmText: '知道了'
						});
						return;
					}

					// 显示拨号确认弹窗
					uni.showModal({
						title: '确认拨号',
						content: `即将拨打客户 ${detail.name || '未知客户'} 的电话：${detail.telephone}`,
						confirmText: '立即拨打',
						cancelText: '取消',
						success: (res) => {
							if (res.confirm) {
								this.performCall(detail, val, businessCode);
							}
						}
					});
				},

				// 执行拨号
				performCall(detail, index, businessCode) {
					// 设置拨打状态
					this.$set(this.customerList[index], 'calling', true);

					let req = {
						domain: detail.domain,
						businessCode,
						callerNum: this.globalData.userInfo.phone,
						calleeNum: detail.telephone,
						uui: 'customer_double_call_' + detail.id,
						authCode: "Get5pJnR"
					}

					let sign = this.createSign(req, "dN9L02Fx0x98o7dGRc7nMgNHA8uPB9iT");
					console.log("Generated Sign:", sign);
					req.sign = sign;

					this.httpPost("/call/yihu-outbound-unit/outboundUnit/mobileCall", req).then(res => {
						console.log('makecall resp:', res);
						// 重置拨打状态
						this.$set(this.customerList[index], 'calling', false);

						if (res.data.code == 200) {
							uni.showToast({
								title: '拨打成功',
								icon: 'success',
								duration: 2000
							});
						} else {
							uni.showToast({
								title: res.data.message || '拨打失败',
								icon: 'none',
								duration: 2000
							});
						}
					}).catch(err => {
						console.log('拨号失败:', err);
						// 重置拨打状态
						this.$set(this.customerList[index], 'calling', false);

						uni.showToast({
							title: '网络异常，请稍后重试',
							icon: 'none',
							duration: 2000
						});
					});
				},

				// 搜索输入处理
				onSearchInput(e) {
					this.customerName = e.detail.value;
				},

				// 清除搜索
				clearSearch() {
					this.customerName = '';
					this.getCustomer(); // 重新加载所有客户
				},

				// 格式化时间
				formatTime(timeStr) {
					if (!timeStr) return '';
					try {
						const date = new Date(timeStr);
						const year = date.getFullYear();
						const month = String(date.getMonth() + 1).padStart(2, '0');
						const day = String(date.getDate()).padStart(2, '0');
						const hours = String(date.getHours()).padStart(2, '0');
						const minutes = String(date.getMinutes()).padStart(2, '0');
						return `${year}-${month}-${day} ${hours}:${minutes}`;
					} catch (e) {
						return timeStr;
					}
				},


				// 获取客户列表
				getCustomer() {
					this.loading = true;

					this.httpGet("/server/crm-customer/customer/list?page=1&pageNum=100").then(res => {
						console.log('customer list:', res.data.data.records);
						if (res.data && res.data.data && res.data.data.records) {
							// 初始化每个客户的状态
							this.customerList = res.data.data.records.map(item => ({
								...item,
								calling: false
							}));
						} else {
							this.customerList = [];
						}
						this.loading = false;
					}).catch(err => {
						console.log('获取客户列表失败:', err);
						this.loading = false;
						uni.showToast({
							title: '获取数据失败，请重试',
							icon: 'none',
							duration: 2000
						});
					});
				}
				
			}
		}
	</script>

	<style scoped>
		/* 页面容器 */
		.customer-page {
			background: #f5f7fa;
			min-height: 100vh;
		}

		/* 搜索区域 */
		.search-section {
			position: fixed;
			left: 0;
			right: 0;
			z-index: 999;
			background: #ffffff;
			padding: 20rpx;
			box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.1);
		}

		.search-wrapper {
			display: flex;
			align-items: center;
			gap: 20rpx;
		}

		.search-input-wrapper {
			flex: 1;
			position: relative;
			background: #f8f9fa;
			border-radius: 50rpx;
			padding: 0 40rpx;
			height: 80rpx;
			display: flex;
			align-items: center;
		}

		.search-icon {
			color: #999999;
			font-size: 32rpx;
			margin-right: 20rpx;
		}

		.search-input {
			flex: 1;
			height: 100%;
			border: none;
			background: transparent;
			font-size: 28rpx;
			color: #333333;
		}

		.clear-icon {
			color: #cccccc;
			font-size: 28rpx;
			padding: 10rpx;
		}

		.search-btn {
			background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
			color: #ffffff;
			border: none;
			border-radius: 40rpx;
			padding: 0 32rpx;
			height: 80rpx;
			font-size: 28rpx;
			box-shadow: 0 4rpx 12rpx rgba(102, 126, 234, 0.3);
		}

		.search-btn-text {
			color: #ffffff;
			font-size: 28rpx;
			font-weight: 500;
		}

		/* 统计区域 */
		.stats-section {
			margin-top: 140rpx;
			padding: 20rpx;
		}

		.stats-card {
			background: #ffffff;
			border-radius: 20rpx;
			padding: 30rpx;
			display: flex;
			align-items: center;
			justify-content: space-around;
			box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);
		}

		.stats-item {
			text-align: center;
		}

		.stats-item-single {
			text-align: center;
		}

		.stats-number {
			display: block;
			font-size: 48rpx;
			font-weight: 700;
			color: #667eea;
			margin-bottom: 8rpx;
		}

		.stats-label {
			font-size: 24rpx;
			color: #999999;
		}

		.stats-divider {
			width: 2rpx;
			height: 60rpx;
			background: #e5e5e5;
		}

		/* 客户列表 */
		.customer-list {
			padding-top: 20rpx;
		}

		.list-container {
			padding: 0 20rpx 40rpx;
		}

		/* 底部安全区域 */
		.bottom-safe-area {
			height: calc(120rpx + env(safe-area-inset-bottom)); /* tabbar高度 + 底部安全区域 */
			background: transparent;
		}

		.customer-card {
			background: #ffffff;
			border-radius: 20rpx;
			margin-bottom: 20rpx;
			padding: 30rpx;
			display: flex;
			align-items: flex-start;
			justify-content: space-between;
			box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.06);
			transition: all 0.3s ease;
		}

		.customer-card:active {
			transform: scale(0.98);
		}

		.customer-info {
			flex: 1;
			margin-right: 20rpx;
		}

		.customer-header {
			margin-bottom: 16rpx;
		}

		.customer-name {
			display: flex;
			align-items: center;
		}

		.name-label {
			font-size: 26rpx;
			color: #999999;
			margin-right: 8rpx;
		}

		.name-value {
			font-size: 32rpx;
			font-weight: 600;
			color: #333333;
		}

		.customer-phone {
			display: flex;
			align-items: center;
			margin-bottom: 12rpx;
		}

		.phone-label {
			font-size: 26rpx;
			color: #999999;
			margin-right: 8rpx;
		}

		.phone-value {
			font-size: 28rpx;
			color: #666666;
			font-family: 'Courier New', monospace;
		}

		.customer-time {
			display: flex;
			align-items: center;
			margin-bottom: 12rpx;
		}

		.time-label {
			font-size: 24rpx;
			color: #999999;
			margin-right: 8rpx;
		}

		.time-value {
			font-size: 24rpx;
			color: #999999;
		}

		.customer-address {
			display: flex;
			align-items: flex-start;
			margin-bottom: 8rpx;
		}

		.address-label {
			font-size: 24rpx;
			color: #999999;
			margin-right: 8rpx;
			flex-shrink: 0;
		}

		.address-value {
			font-size: 24rpx;
			color: #999999;
			line-height: 1.4;
			word-break: break-all;
		}

		/* 操作区域 */
		.action-section {
			display: flex;
			align-items: center;
		}

		.call-btn {
			background: linear-gradient(135deg, #52c41a 0%, #73d13d 100%);
			color: #ffffff;
			border: none;
			border-radius: 50rpx;
			padding: 0 32rpx;
			height: 80rpx;
			display: flex;
			align-items: center;
			justify-content: center;
			gap: 12rpx;
			box-shadow: 0 4rpx 12rpx rgba(82, 196, 26, 0.3);
			transition: all 0.3s ease;
			min-width: 140rpx;
		}

		.call-btn:disabled {
			background: #f5f5f5;
			color: #cccccc;
			box-shadow: none;
		}

		.call-btn.calling {
			background: linear-gradient(135deg, #faad14 0%, #ffc53d 100%);
			box-shadow: 0 4rpx 12rpx rgba(250, 173, 20, 0.3);
		}

		.call-btn:active:not(:disabled) {
			transform: scale(0.95);
		}

		.call-icon {
			font-size: 28rpx;
		}

		.loading-icon {
			font-size: 28rpx;
		}

		.call-text {
			font-size: 26rpx;
			font-weight: 500;
		}

		/* 空状态 */
		.empty-state {
			text-align: center;
			padding: 120rpx 40rpx;
		}

		.empty-icon {
			font-size: 120rpx;
			color: #e5e5e5;
			margin-bottom: 30rpx;
			display: block;
		}

		.empty-text {
			font-size: 28rpx;
			color: #999999;
			margin-bottom: 40rpx;
		}

		.refresh-btn {
			background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
			color: #ffffff;
			border: none;
			border-radius: 40rpx;
			padding: 0 40rpx;
			height: 80rpx;
			font-size: 28rpx;
			box-shadow: 0 4rpx 12rpx rgba(102, 126, 234, 0.3);
		}

		.refresh-text {
			color: #ffffff;
			font-size: 28rpx;
			font-weight: 500;
		}

		/* 加载状态 */
		.loading-state {
			text-align: center;
			padding: 80rpx 40rpx;
			display: flex;
			flex-direction: column;
			align-items: center;
		}

		.loading-state .loading-icon {
			font-size: 60rpx;
			color: #667eea;
			margin-bottom: 20rpx;
		}

		.loading-text {
			font-size: 28rpx;
			color: #999999;
		}

		/* 动画效果 */
		@keyframes spin {
			0% { transform: rotate(0deg); }
			100% { transform: rotate(360deg); }
		}

		.cuIconfont-spin {
			animation: spin 1s linear infinite;
		}

		/* 响应式适配 */
		@media (max-width: 750rpx) {
			.search-section {
				padding: 16rpx;
			}

			.search-input-wrapper {
				height: 72rpx;
				padding: 0 32rpx;
			}

			.search-btn {
				height: 72rpx;
				padding: 0 24rpx;
			}

			.stats-section {
				margin-top: 120rpx;
				padding: 16rpx;
			}

			.stats-card {
				padding: 24rpx;
			}

			.stats-number {
				font-size: 40rpx;
			}

			.customer-card {
				padding: 24rpx;
				margin-bottom: 16rpx;
			}

			.name-value {
				font-size: 28rpx;
			}

			.call-btn {
				height: 72rpx;
				padding: 0 24rpx;
				min-width: 120rpx;
			}
		}

		@media (max-width: 600rpx) {
			.search-input-wrapper {
				height: 68rpx;
				padding: 0 28rpx;
			}

			.search-btn {
				height: 68rpx;
				padding: 0 20rpx;
			}

			.stats-card {
				padding: 20rpx;
			}

			.stats-number {
				font-size: 36rpx;
			}

			.customer-card {
				padding: 20rpx;
				align-items: flex-start;
			}

			.customer-info {
				margin-right: 16rpx;
			}

			.call-btn {
				height: 68rpx;
				padding: 0 20rpx;
				min-width: 100rpx;
			}

			.call-text {
				font-size: 24rpx;
			}

			.address-value {
				font-size: 22rpx;
			}
		}
	</style>