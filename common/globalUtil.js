// let devServer = 'http://182.40.192.214:7002'
// let onlineServer = 'http://182.40.192.214:7002'
// let callServer = 'http://182.40.192.214:6010'
let devServer = 'https://ccmobile.chinarui.cn:7002'
let onlineServer = 'https://ccmobile.chinarui.cn:7002'
let callServer = 'https://ccmobile.chinarui.cn:6010'
let apiUri = onlineServer
const CryptoJS = require('crypto-js')
let getCurrentTime = function() {
	var now = new Date();

	var year = now.getFullYear(); //年
	var month = now.getMonth() + 1; //月
	var day = now.getDate(); //日

	var hh = now.getHours(); //时
	var mm = now.getMinutes(); //分

	var clock = year + "-";

	if (month < 10)
		clock += "0";

	clock += month + "-";

	if (day < 10)
		clock += "0";

	clock += day + " ";

	if (hh < 10)
		clock += "0";

	clock += hh + ":";
	if (mm < 10) clock += '0';
	clock += mm;
	return (clock);
}
let showText = function(data, time = 1500) {
	uni.showToast({
		title: data,
		icon: 'none',
		duration: time,
	});
}

let setToken = function(key, value) {
	uni.setStorage({
		key: key,
		data: value,
	});
}

let removeToken = function(key) {
	uni.removeStorage({
		key: key,
		success: function(res) {
			console.log('success');
		}
	});
}

let getToken = function(key, value) {
	uni.getStorage({
		key: key,
		success: function(res) {
			return res
		},
		fail: function(err) {
			uni.reLaunch({
				url: "/pages/login/login"
			})
		},
		complete: function() {}
	});
}
let checkToken = function() {
	var that = this
	uni.getStorage({
		key: 'userInfo',
		success: function(res) {
			that.globalData.userInfo = res.data
		},
		fail: function(err) {
			uni.reLaunch({
				url: "/pages/login/login"
			})
		},
		complete: function() {}
	});
}

let getQiNiuToken = function() {
	httpGet('/wem/rest/token/get', null).then((res) => {
		this.globalData.qiniuToken = res.data.token
	}).catch((err) => {
		showText('服务器异常')
	})
}
let createSign = function(object, privateKey) {
	// 将对象转换为键值对
	const map = {
		...object
	};
	console.log("createSign map", JSON.stringify(map));
	const authCode = map.authCode;
	delete map.sign; // 移除 sign 字段
	// 获取并排序键
	const keys = Object.keys(map);
	keys.sort();
	// 构建 rawStr
	let rawStr = '';
	keys.forEach(key => {
		rawStr += `${map[key]}`; // 根据需要调整格式
	});
	rawStr += privateKey;
	console.info("## rawStr is", rawStr);
	// Base64 编码
	const base64Str = Buffer.from(rawStr).toString('base64');
	console.info("## base64 rawStr is", base64Str);
	// 计算 MD5 值
	console.log('CryptoJS ', CryptoJS)
	const value = CryptoJS.MD5(base64Str).toString()
	console.info("create value>>", value);
	return value;
}

function removePrefixes(url) {
	if (url.startsWith('/server')) {
		apiUri = onlineServer
		return url.replace(/^\/server/, '');
	} else if (url.startsWith('/call')) {
		apiUri = callServer
		return url.replace(/^\/call/, '');
	}
	console.log('removePrefixes ', url)
	return url;
}
let httpGet = function(url, data) {
	const token = uni.getStorageSync('token');

	var postpromise = new Promise(function(getsuccess, getfail) {
		uni.showLoading({
			title: '加载中'
		});
		// #ifdef H5
		url = url
		// #endif
		// #ifndef H5
		url = removePrefixes(url)
		url = apiUri + url
		console.log('url:', url)
		// #endif
		uni.request({
			header: {
				'content-type': 'application/json',
				'Authorization': token
			},
			url: url,
			data: data,
			method: "GET",
			success: function(sres) {
				uni.hideLoading();
				getsuccess(sres);
				if (sres.data.message == 'Token无效') {
					uni.removeStorageSync('token');
					uni.redirectTo({
						url: '/pages/login/index'
					});
				}
			},
			fail: (fres) => {
				uni.hideLoading();
				getfail(fres);
				console.log("get " + fres)
			}
		})
	})
	return postpromise;
}
let httpPost = function(url, data, isShowLoading = true) {
	const token = uni.getStorageSync('token');
	var postpromise = new Promise(function(postsuccess, postfail) {
		if (isShowLoading) {
			uni.showLoading({
				title: '加载中'
			});
		}
		// #ifdef H5
		url = url,
			// #endif
			// #ifndef H5
			url = removePrefixes(url)
		url = apiUri + url,
			console.log('post url:', url)
		// #endif
		uni.request({
			header: {
				'content-type': 'application/json',
				'Authorization': token
			},
			url: url,
			data: data,
			method: "POST",
			success: function(sres) {
				uni.hideLoading();
				postsuccess(sres);
				console.log(sres.data.message)
				if (sres.data.message == 'Token无效') {
					uni.removeStorageSync('token');
					uni.redirectTo({
						url: '/pages/login/index'
					});
				}

			},
			fail: (fres) => {
				uni.hideLoading();
				postfail(fres);
				console.log("post:" + fres)
			}
		})
	})
	return postpromise;
}
export {
	showText,
	httpGet,
	httpPost,
	getQiNiuToken,
	setToken,
	checkToken,
	getToken,
	removeToken,
	getCurrentTime,
	createSign
}