import Vue from 'vue'
import App from './App'
import {
	showText,
	httpGet,
	httpPost,
	getQiNiuToken,
	setToken,
	checkToken,
	getToken,
	removeToken,
	getCurrentTime,
	createSign,
} from "@/common/globalUtil.js";

import './utils/useSocket.js';
import store from "./utils/store.js";
Vue.prototype.$store = store;

Vue.prototype.createSign = createSign;
Vue.prototype.showText = showText;
Vue.prototype.httpGet = httpGet;
Vue.prototype.httpPost = httpPost;
Vue.prototype.getQiNiuToken = getQiNiuToken;
Vue.prototype.setToken = setToken;
Vue.prototype.checkToken = checkToken;
Vue.prototype.getToken = getToken;
Vue.prototype.removeToken = removeToken;
Vue.prototype.getCurrentTime = getCurrentTime;
import {globalData} from '@/common/store.js'
Vue.prototype.globalData = globalData
import customer from './pages/customer/index.vue'
Vue.component('customer',customer)

import setting from './pages/setting/index.vue'
Vue.component('setting',setting)

import agent from './pages/agent/index.vue'
Vue.component('agent',agent)

import task from './pages/task/index.vue'
Vue.component('task',task)

import dialer from './pages/dialer/index.vue'
Vue.component('dialer',dialer)

import cuCustom from './colorui/components/cu-custom.vue'
Vue.component('cu-custom',cuCustom)

Vue.config.productionTip = false

App.mpType = 'app'

const app = new Vue({
    ...App
})
app.$mount()

 



