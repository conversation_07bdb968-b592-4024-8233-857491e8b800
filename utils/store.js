import Vue from 'vue'
import Vuex from 'vuex'

Vue.use(Vuex);

const store = new Vuex.Store({
	state: {
		AgentStateList: [],
		stateTextList: [],
		message: {},
		stateText: '未登录',
		callingDevice: '',
		isClick: false,
		isLogin:false,
		errText: '',
	},
	mutations: {
		setAgentStateList(that, info) {
			console.log(info)
			that.AgentStateList = info;
		},
		setMessage(that, info) {
			that.message = info
		},
		setStateText(that, info) {
			that.stateText = info
		},
		setLoginState(that, info) {
			that.isLogin = info
		},
		setCallingDevice(that, info) {
			that.callingDevice = info
		},
		setStateTextList(that, info) {
			that.stateTextList = info
		},
		setIsClick(that, info) {
			that.isClick = info
		},
		setErrText(that, info) {
			that.errText = info
		},
	}
})
export default store;
