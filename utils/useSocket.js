import Vue from 'vue'
import store from "./store.js";
import {
	e_OnAgentLoggedOff,
	e_OnAgentLoggedOn,
	e_OnAgentNotReady,
	e_OnAgentReady,
	e_OnAgentWorkingAfterCall,
	e_OnConferencedEvt,
	e_OnConnectionClearedEvt,
	e_OnDeliveredEvt,
	e_OnEstablishedEvt,
	e_OnHeldEvt
} from './event.js'
let timerHeat = null
let timerRe = null
let Socket = null
let IsOpen = false
let connectNum = 0
let eveType = 0
let activeCloseWs = false

export function heatData() {
	let data = {}
	if (store.state.stateText == '未登录') {
		data = {
			device: '',
			domain: '',
			type: "18"
		}
	} else {
		data = {
			device: store.state.message.stationId || '',
			domain: store.state.message.domain || '',
			type: "18"
		}
	}
	console.log(data)
	uni.sendSocketMessage({
		data: JSON.stringify(data),
		success(e) {
			console.log('send heat')
			console.log(e)
		},
		fail(e) {
			console.log('发送心跳失败')
			store.commit('setErrText', '系统连接超时，请稍后重试！')
		}
	})
}

export function closeConnect(flag=false) {
	if(flag) activeCloseWs = true
	uni.closeSocket()
	if (timerHeat) {
		clearInterval(timerHeat)
		timerHeat = null
	}
	Socket = null
	Vue.prototype.$Socket = null
	IsOpen = false
}
export function reConnect(condition) {
	IsOpen = false
	if (eveType == 99) {
		store.commit('setErrText', '')
		e_OnAgentLoggedOff()
		initWs()
		return
	}
	if (Socket && Socket.readyState == 1) return
	if (['login', 'show'].includes(condition)) {
		connectNum = 0
	}
	console.log('重新连接')
	// uni.showLoading({
	// 	title: '正在连接'
	// });
	clearInterval(timerHeat)
	timerHeat = null
	if (connectNum < 10) {
		timerRe = setTimeout(() => {
			initWs(condition)
		}, 3000)
		connectNum++
	} else {
		uni.hideLoading()
		uni.setStorageSync('state', '')
		uni.redirectTo({
			url: '/pages/login/login'
		})
	}
}

export function initWs(condition) {
	// var wsServer = window.g.wsServer
	// var wsServer = 'ws://182.40.192.214:8006'
	// var wsServer = 'ws://101.226.11.229:8006'
	var wsServer = 'wss://ccmobile.chinarui.cn:8006'
	if (IsOpen) return
	var index= 1;
	uni.getNetworkType({
		success(result) {
			if (result.networkType != 'none') {
				Socket = uni.connectSocket({
					url: wsServer, //连接地址 必填
					method: 'GET',
					complete: e => {
						console.log(e)
					}
				})
				Vue.prototype.$Socket = Socket;
				uni.onSocketOpen((res) => {
					console.log('连接......')
					store.commit('setErrText', '')
					connectNum = 0
					IsOpen = true
					const message = store.state.message
					activeCloseWs = false
					return
					if (condition == 'login' || uni.getStorageSync('state') == '登录成功') {
						console.log('app login')
						let data = {
							reasoncode: 0,
							agentState: "Login",
							acdId: "101",
							workMode: "Manual",
							type: '1',
							...message
						}
						uni.sendSocketMessage({
							data: JSON.stringify(data),
							success() {
								console.log('成功')
								store.commit('setErrText', '')
							},
							fail() {
								console.log('请重新登录')
								uni.setStorageSync('state', '')
								uni.redirectTo({
									url: '/pages/login/login'
								})
							}
						})
					}
					uni.hideLoading();
					timerHeat = setInterval(heatData, 5000)
				})

				uni.onSocketError(function() {
					if(activeCloseWs) return
					console.log('监听错误')
					store.commit('setLoginState',false)
					if (connectNum < 10 && eveType != 99) {
						store.commit('setErrText', '连接中...')
					} else {
						store.commit('setErrText', '系统连接超时，请稍后重试！')
					}
					closeConnect()
					reConnect()
				})
				uni.onSocketClose(function() {
					if(activeCloseWs) return
					console.log('断开连接')
					store.commit('setLoginState',false)
					if (connectNum < 10 && eveType != 99) {
						store.commit('setErrText', '连接中...')
					} else {
						store.commit('setErrText', '系统连接超时，请稍后重试！')
					}
					closeConnect()
					reConnect()
				})
				uni.onSocketMessage((res) => {
					console.log('收到服务器内容：' + res.data);
					store.commit('setErrText', '')
					var evt = JSON.parse(res.data);
					console.log('111',evt)
					eveType = evt.type
					if (evt.code != null) {
						if (evt.type == 1) {
							if (evt.code == 200) {
								// 登录成功
								console.log('登录成功')
								if (!uni.getStorageSync('state')) {
									uni.setStorageSync('state', '登录成功');
									uni.redirectTo({
										url: '/pages/home/<USER>'
									})
								}
							} else if (evt.code == 201) {
								// 设置小休码
								var msg = JSON.parse(evt.errMsg);
								store.commit('setAgentStateList', msg)
							} else if (evt.code == 202) {
								// 设置外显号码
								var msg = JSON.parse(evt.errMsg);
								console.log(msg)
							} else if (evt.code > 500) {
								// 当处于其他页面时 登录失败 主动退出登录页
								uni.setStorageSync('state', '');
								store.commit('setStateText', '未登录')
								uni.redirectTo({
									url: '/pages/login/login'
								})
								var err_str = evt.errMsg;
								let errLoginText = ''
								if (err_str.indexOf(" get agent not exist fail !") > 0) {
									console.log('用户名不存在，' + err_str);
									errLoginText = '登录信息有误，请重新输入'
								} else if (err_str.indexOf(" station not exist !") > 0) {
									console.log('分机号不存在，' + err_str);
									errLoginText = '登录信息有误，请重新输入'
								} else if (err_str.indexOf(" agent password error !") > 0) {
									console.log('密码错误，请重新输入密码，' + err_str);
									errLoginText = '登录信息有误，请重新输入'
								} else if (err_str.indexOf(" not registered !") > 0) {
									console.log('分机未注册，' + err_str);
									errLoginText = '登录信息有误，请重新输入'
								} else if (err_str.indexOf(" already used by ") > 0) {
									console.log('分机被使用，' + err_str);
									errLoginText = '账号已登录，请联系管理员'
								} else {
									console.log('登录失败，请检查坐席号、密码、分机状态是否正常，' + err_str);
									errLoginText = '登录信息有误，请重新输入'
								}
								store.commit('setErrText', errLoginText)
								return;
							}
						} else if (evt.type == 2) {

						} else if (evt.type == 16) {

						} else if (evt.type == 17) {
							e_OnAgentReady();
						}
					}

					if (evt.evtName != null) {
						uni.$emit('updateStateText', 'update')
						store.commit('setCallingDevice', '')
						if (evt.evtName == 'AgentNotReadyEvt') {
							e_OnAgentNotReady(evt);
						} else if (evt.evtName == 'AgentReadyEvt') {
							e_OnAgentReady();
						} else if (evt.evtName == 'AgentLoggedOnEvt') {
							e_OnAgentLoggedOn();
						} else if (evt.evtName == 'AgentLoggedOffEvt') {
							e_OnAgentLoggedOff();
						} else if (evt.evtName == 'AgentWorkingAfterCallEvt') {
							e_OnAgentWorkingAfterCall();
						} else if (evt.evtName == 'OriginatedEvt') {
							e_OnDeliveredEvt(evt);
						} else if (evt.evtName == 'DeliveredEvt') {
							e_OnDeliveredEvt(evt);
						} else if (evt.evtName == 'EstablishedEvt') {
							e_OnEstablishedEvt(evt);
						} else if (evt.evtName == 'ConnectionClearedEvt') {
							e_OnConnectionClearedEvt(evt);
						} else if (evt.evtName == 'HeldEvt') {
							e_OnHeldEvt(evt);
						} else if (evt.evtName == 'RetrievedEvt') {
							e_OnRetrievedEvt(evt);
						} else if (evt.evtName == 'ConferencedEvt') {
							e_OnConferencedEvt(evt);
						} else if (evt.evtName == 'TransferedEvt') {
							e_OnTransferedEvt(evt);
						}
					}
				})
			} else {
				console.log('网络错误')
				closeConnect()
				if (connectNum < 10 && eveType != 99) {
					store.commit('setErrText', '连接中...')
				} else {
					eveType = 0
					store.commit('setErrText', '系统连接超时，请稍后重试！')
				}
				reConnect()
			}
		}
	})
}

// initWs()