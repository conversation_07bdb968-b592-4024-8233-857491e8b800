import store from './store.js'
// 2.处理接收各种事件
// ------------------------ request event ------------------------------------------
export function e_OnAgentNotReady(evt) {
	console.log('store.state.isClick' + store.state.isClick)
	if (store.state.isClick) {
		const list = store.state.AgentStateList
		const arr = list.filter((item) => {
			return item.code == evt.reason
		})
		console.log('arr ',arr)
		const text = arr[0] ? arr[0].codeName : '未就绪' 
		store.commit('setStateText', text)
		store.commit('setIsClick', false)
	} else {
		store.commit('setStateText', '未就绪')
	}
	console.log('e_OnAgentNotReady')
}

export function e_OnAgentLoggedOn() {
	console.log('未就绪')
	store.commit('setLoginState',true)
	store.commit('setStateText', '未就绪')
}

export function e_OnAgentReady() {
	console.log('e_OnAgentReady 就绪')

	store.commit('setStateText', '就绪')
}

export function e_OnAgentLoggedOff() {
	store.commit('setStateText', '未登录')
	uni.setStorageSync('state', '')
	uni.redirectTo({
		url: '/pages/login/login'
	})
	console.log('未登录')
}


export function e_OnAgentWorkingAfterCall() {
	console.log('话后处理')
	store.commit('setStateText', '话后处理')
}

// 振铃
export function e_OnDeliveredEvt(evt_info) {
	console.log('振铃中')
	store.commit('setStateText', '振铃中')
	store.commit('setCallingDevice', evt_info.callingDevice)
}

// 接通
export function e_OnEstablishedEvt(evt_info) {
	console.log('通话中');
	store.commit('setStateText', '通话中')
	store.commit('setCallingDevice', evt_info.callingDevice)
}
// 挂断
export function e_OnConnectionClearedEvt(evt_info) {
	console.log('已挂断')
	store.commit('setStateText', '已挂断')
}

//保持
export function e_OnHeldEvt(evt_info) {
	console.log('保持')
}

// 取回
export function e_OnRetrievedEvt(evt_info) {
	console.log('取回   已接通')
}

// 转接
export function e_OnTransferedEvt(evt_info) {
	console.log('已转移')
}

export function e_OnConferencedEvt(evt_info) {
	console.log('会议中')
}
