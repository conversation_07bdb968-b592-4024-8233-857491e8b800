import CryptoJS from 'crypto-js';
/** ---密码加密 start--- */
const SECRET_KEY = CryptoJS.enc.Utf8.parse("a15q8f6s5s1a4v31");
const SECRET_IV = CryptoJS.enc.Utf8.parse("a3c6g5h4v9sss4v6");

export function encryptEnc(pwd) {
  let srcs = CryptoJS.enc.Utf8.parse(pwd);
  let encrypted = CryptoJS.AES.encrypt(srcs, SECRET_KEY, {
    iv: SECRET_IV ,
    mode: CryptoJS.mode.CBC,
    padding: CryptoJS.pad.ZeroPadding
  })
  return CryptoJS.enc.Base64.stringify(encrypted.ciphertext);
}
/** ---密码加密end--- */