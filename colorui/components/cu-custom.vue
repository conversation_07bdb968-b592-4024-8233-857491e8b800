<template>
	<view>
		<view class="cu-custom modern-navbar" :style="[{height:CustomBar + 'px'}]">
			<view class="cu-bar fixed modern-navbar-content" :style="style" :class="[bgImage!=''?'none-bg text-white bg-img':'',bgColor]">
				<view class="action modern-back-btn" @tap="BackPage" v-if="isBack">
					<view class="back-icon-wrapper">
						<text class="cuIcon-back back-icon"></text>
					</view>
					<slot name="backText"></slot>
				</view>
				<view class="content modern-title" :style="[{top:StatusBar + 'px'}]">
					<slot name="content"></slot>
				</view>
				<view class="right-actions">
					<slot name="right"></slot>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				StatusBar: this.StatusBar,
				CustomBar: this.CustomBar
			};
		},
		name: 'cu-custom',
		computed: {
			style() {
				var StatusBar= this.StatusBar;
				var CustomBar= this.CustomBar;
				var bgImage = this.bgImage;
				var style = `height:${CustomBar}px;padding-top:${StatusBar}px;`;
				if (this.bgImage) {
					style = `${style}background-image:url(${bgImage});`;
				}
				return style
			}
		},
		props: {
			bgColor: {
				type: String,
				default: ''
			},
			isBack: {
				type: [Boolean, String],
				default: false
			},
			bgImage: {
				type: String,
				default: ''
			},
		},
		methods: {
			BackPage() {
				if (getCurrentPages().length < 2 && 'undefined' !== typeof __wxConfig) {
					let url = '/' + __wxConfig.pages[0]
					return uni.redirectTo({url})
				}
				uni.navigateBack({
					delta: 1
				});
			}
		}
	}
</script>

<style scoped>
/* 现代化导航栏样式 */
.modern-navbar {
	position: relative;
	z-index: 999;
}

.modern-navbar-content {
	background: linear-gradient(135deg, var(--primary-color, #1890FF) 0%, var(--primary-light, #40A9FF) 100%);
	box-shadow: 0 2rpx 16rpx rgba(24, 144, 255, 0.15);
	backdrop-filter: blur(20rpx);
	transition: all 0.3s ease;
}

.modern-navbar-content.bg-white {
	background: rgba(255, 255, 255, 0.95);
	color: var(--text-primary, #262626);
	box-shadow: 0 2rpx 16rpx rgba(0, 0, 0, 0.08);
}

.modern-navbar-content.bg-white .back-icon {
	color: var(--text-primary, #262626);
}

/* 现代化返回按钮 */
.modern-back-btn {
	display: flex;
	align-items: center;
	padding: 0 16rpx;
	transition: all 0.2s ease;
}

.modern-back-btn:active {
	transform: scale(0.95);
	opacity: 0.8;
}

.back-icon-wrapper {
	width: 64rpx;
	height: 64rpx;
	display: flex;
	align-items: center;
	justify-content: center;
	border-radius: 50%;
	background: rgba(255, 255, 255, 0.15);
	margin-right: 8rpx;
	transition: all 0.2s ease;
}

.modern-back-btn:active .back-icon-wrapper {
	background: rgba(255, 255, 255, 0.25);
}

.back-icon {
	font-size: 32rpx;
	color: #FFFFFF;
	font-weight: bold;
}

/* 现代化标题 */
.modern-title {
	flex: 1;
	text-align: center;
	font-size: 36rpx;
	font-weight: 600;
	color: #FFFFFF;
	letter-spacing: 1rpx;
	text-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}

.modern-navbar-content.bg-white .modern-title {
	color: var(--text-primary, #262626);
	text-shadow: none;
}

/* 右侧操作区域 */
.right-actions {
	display: flex;
	align-items: center;
	padding: 0 16rpx;
}

/* 响应式适配 */
@media (max-width: 750rpx) {
	.modern-title {
		font-size: 32rpx;
	}

	.back-icon-wrapper {
		width: 56rpx;
		height: 56rpx;
	}

	.back-icon {
		font-size: 28rpx;
	}
}

/* 深色模式支持 */
@media (prefers-color-scheme: dark) {
	.modern-navbar-content.bg-white {
		background: rgba(26, 26, 26, 0.95);
		color: #FFFFFF;
	}

	.modern-navbar-content.bg-white .back-icon {
		color: #FFFFFF;
	}

	.modern-navbar-content.bg-white .modern-title {
		color: #FFFFFF;
	}
}

/* 毛玻璃效果增强 */
.modern-navbar-content::before {
	content: '';
	position: absolute;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	background: inherit;
	filter: blur(20rpx);
	opacity: 0.8;
	z-index: -1;
}

/* 底部分割线 */
.modern-navbar-content::after {
	content: '';
	position: absolute;
	bottom: 0;
	left: 0;
	right: 0;
	height: 1rpx;
	background: linear-gradient(90deg, transparent 0%, rgba(255, 255, 255, 0.2) 50%, transparent 100%);
}

.modern-navbar-content.bg-white::after {
	background: linear-gradient(90deg, transparent 0%, rgba(0, 0, 0, 0.1) 50%, transparent 100%);
}
</style>
